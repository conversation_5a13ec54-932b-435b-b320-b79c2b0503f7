<template>
  <NjPage
    title="Suivi des opérations CEE"
    :loading="data.loading"
    :error-message="data.error"
    :expend-body="arrayExpanded"
  >
    <template #header-actions>
      <DoublonDialog />
      <NjBtn :to="{ name: 'OperationOneNewView' }" prepend-icon="mdi-pencil"> Créer une opération </NjBtn>
    </template>
    <template v-if="hasFeatureTdb" #sub-header>
      <VRow class="flex-column">
        <VCol>
          <VRow>
            <VCol>
              <VCheckbox v-model="filter.myRequests" label="Mes opérations" />
            </VCol>
            <VSpacer />
            <VCol v-if="!!filterCount" class="" style="text-align: end; align-self: center">
              <!--  filtres appliqués -->
              <VLink variant="text" prepend-icon="mdi-refresh" icon="mdi-restore" @click="filter = defaultFilter"
                >Réinitialiser les filtres
              </VLink>
            </VCol>
          </VRow>
        </VCol>
        <VCol>
          <VRow class="align-center" dense>
            <VCol cols="3">
              <SearchInput
                v-model:loading="data.loading"
                :model-value="pageFilter.search"
                clearable
                @update:model-value="updateSearch"
              />
            </VCol>
            <!-- <VCol class="subheader-actions align-center flex-grow-0" style="min-width: fit-content">

              <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
                Réinitialiser les filtres
              </VLink>
            </VCol>
            <VCol>
              <ButtonsMenuAdapter :buttons="headerButtons" />
            </VCol> -->
            <VCol cols="2">
              <VTextField
                label="Organisations"
                append-inner-icon="mdi-format-list-bulleted"
                readonly
                class="v-field--one-line-chip bg-white"
                clearable
                :active="!!selectedEntities.length"
              >
                <VChip v-if="selectedEntities[0]" density="compact" class="flex-grow-1 flex-basis-0"
                  >{{ selectedEntities[0].name }} ({{ selectedEntities[0].id }})</VChip
                >
                <VChip v-if="selectedEntities.length > 1" density="compact" class="px-2">
                  + {{ selectedEntities.length - 1 }}</VChip
                >
                <EntityFilterDialog
                  :selected="selectedEntities"
                  activator="parent"
                  @update:selected="updateSelectedEntities"
                />
              </VTextField>
            </VCol>
            <VCol cols="2">
              <RemoteAutoComplete
                v-model="filter.beneficiaryIds"
                label="Bénéficiaires"
                :query-for-one="(id) => beneficiaryApi.findOne(id)"
                :query-for-ones="(ids) => beneficiaryApi.findAll({ size: 1000, sort: ['displayName'] }, { ids })"
                :query-for-all="
                  (s, pageable) => beneficiaryApi.findAll({ ...pageable, sort: ['displayName'] }, { search: s })
                "
                :item-title="(item) => item.displayName ?? ''"
                chips
                closable-chips
                multiple
                infinite-scroll
                clearable
                :max-elements="1"
              />
            </VCol>
            <VCol cols="2">
              <VSelect
                label="Soumise à arrêté contrôle"
                :model-value="controlOrderNatures"
                :items="controlOrderNatureItems"
                multiple
                clearable
                @update:model-value="updateControlOrderNatures"
              />
            </VCol>
            <VCol cols="2">
              <VSelect
                v-model="filter.periodIds"
                label="Périodes CEE"
                class="bg-white"
                :items="periodsStore.periods"
                item-title="name"
                item-value="id"
                multiple
                clearable
              />
            </VCol>

            <CardDialog v-model="changeEntityDialog" title="Changer d'organisation" width="650px">
              <VRow class="flex-column">
                <VCol>
                  <ErrorAlert type="warning" :message="changeEntityMessage" />
                </VCol>
                <VCol>
                  <VForm>
                    <RemoteAutoComplete
                      v-model="selectedEntity"
                      label="Organisation"
                      :query-for-one="(id) => entityApi.getOne(id)"
                      :query-for-all="
                        (s, pageable) => entityApi.getAll({ search: s, visible: true, level: 4 }, pageable)
                      "
                      item-title="name"
                      item-value="id"
                      :rules="[requiredRule]"
                      infinite-scroll
                    />
                  </VForm>
                </VCol>
              </VRow>
              <template #actions>
                <NjBtn variant="outlined" @click="changeEntityDialog = false">Annuler</NjBtn>
                <NjBtn :loading="changeEntityLoading" @click="changeEntity">Valider</NjBtn>
              </template>
            </CardDialog>

            <VCol cols="1">
              <NjBtn variant="outlined" prepend-icon="mdi-filter-variant" @click="handleFilterDrawer">
                Filtres <VBadge :model-value="!!filterCount" :content="filterCount" color="primary" inline></VBadge>
              </NjBtn>
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
    <template v-else #sub-header>
      <VRow class="align-center">
        <VCol cols="3">
          <SearchInput
            v-model:loading="data.loading"
            :model-value="pageFilter.search"
            @update:model-value="updateSearch"
          />
        </VCol>
        <VCol class="subheader-actions align-center flex-grow-0" style="min-width: fit-content">
          <VCheckbox v-model="filter.myRequests" label="Mes opérations" class="flex-grow-0" />
          <NjBtn @click="handleFilterDrawer"> Filtres </NjBtn>
          <VLink v-show="showResetFilter" icon="mdi-sync" @click="filter = defaultFilter">
            Réinitialiser les filtres
          </VLink>
        </VCol>
        <VCol>
          <ButtonsMenuAdapter :buttons="headerButtons" />
        </VCol>

        <CardDialog v-model="changeEntityDialog" title="Changer d'organisation" width="650px">
          <VRow class="flex-column">
            <VCol>
              <ErrorAlert type="warning" :message="changeEntityMessage" />
            </VCol>
            <VCol>
              <VForm>
                <RemoteAutoComplete
                  v-model="selectedEntity"
                  label="Organisation"
                  :query-for-one="(id) => entityApi.getOne(id)"
                  :query-for-all="(s, pageable) => entityApi.getAll({ search: s, visible: true, level: 4 }, pageable)"
                  item-title="name"
                  item-value="id"
                  :rules="[requiredRule]"
                  infinite-scroll
                />
              </VForm>
            </VCol>
          </VRow>
          <template #actions>
            <NjBtn variant="outlined" @click="changeEntityDialog = false">Annuler</NjBtn>
            <NjBtn :loading="changeEntityLoading" @click="changeEntity">Valider</NjBtn>
          </template>
        </CardDialog>
      </VRow>
    </template>
    <template #body>
      <VRow class="w-100 flex-column" dense>
        <VCol v-if="hasFeatureTdb" :class="{ 'flex-grow-0': arrayExpanded }">
          <VRow class="flex-column">
            <VCol>
              <div class="d-flex align-center">
                <VIcon icon="mdi-poll" size="24" class="me-4" color="#768C9B"></VIcon>
                <div
                  style="
                    color: var(--Neutral-colors-Gray800, #314452);

                    /* Heading/Heading XS */
                    font-family: var(--nj-semantic-font-family-default);
                    font-size: 1.25rem;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 28px;
                    letter-spacing: 0;
                    color: #314452;
                  "
                >
                  Vue d'ensemble
                </div>
                <VSpacer></VSpacer>
                <VLink v-if="keys(dashboardFilter).length" class="me-4" @click="dashboardFilter = {}"
                  >Effacer toute la sélection</VLink
                >
                <VLink
                  @click="
                    ((arrayExpanded = !arrayExpanded),
                    trace('operation_dashboard_toggle', { action: arrayExpanded ? 'reduce' : 'expand' }))
                  "
                >
                  <template #default>
                    {{ !arrayExpanded ? 'Réduire' : 'Afficher' }}
                  </template>
                  <template #append-icon>
                    <VIcon
                      icon="mdi-chevron-up"
                      style="transition: all 0.3s linear"
                      :style="{ transform: arrayExpanded ? 'rotate(180deg)' : undefined }"
                    ></VIcon>
                  </template>
                </VLink>
              </div>
              <VDivider class="my-2"></VDivider>
              <div
                :class="{ 'd-flex': !arrayExpanded, 'd-none': arrayExpanded }"
                class="gap-4 flex-column"
                style="flex: 1 1 auto"
              >
                <div class="d-flex align-stretch gap-4">
                  <NumberCardDashboardComponent
                    trace-id="operations-to-process"
                    class="flex-grow-1 flex-basis-0"
                    title="Simulations à traiter"
                    tooltip="Simulations indiquées comme « A traiter » par leur créateur, à transformer en opérations"
                    :disabled="
                      !!filter.stepIds?.length ||
                      (!isEqual(filter.operationStatuses, []) && !isEqual(filter.operationStatuses, ['DOING']))
                    "
                    :targeted-value="0"
                    :value="
                      (v) =>
                        operationApi
                          .getSummary({
                            ...v,
                            stepIds: [0],
                            toProcess: true,
                            operationStatuses: ['DOING'],
                          })
                          .then((it) => it.data.operationsNumber)
                    "
                    target="_blank"
                    :to="{
                      name: 'SimulationAllView',
                      query: { ...(filter as any), operationStatuses: ['DOING'], toProcess: 'true' },
                    }"
                  >
                    <template #action="{ props }">
                      <VLink
                        v-bind="props"
                        no-padding
                        append-icon="mdi-open-in-new"
                        variant="text"
                        color="primary"
                        density="compact"
                        >Ouvrir la liste</VLink
                      >
                    </template>
                  </NumberCardDashboardComponent>
                  <NumberCardDashboardComponent
                    trace-id="operations-final-version-to-send"
                    class="flex-grow-1 flex-basis-0"
                    title="VF à transmettre"
                    tooltip="Opérations pour lesquelles une version finale est disponible et à transmettre aux clients (à l'étape 100, ou à l'étape 70 en cas de contrôle)"
                    :targeted-value="0"
                    :disabled="!filter.stepIds?.every((it) => it >= 70)"
                    :value="
                      (v) =>
                        operationApi
                          .getSummary({ ...v, mustSendFinalVersion: true, operationStatuses: [] })
                          .then((it) => it.data.operationsNumber)
                    "
                    :request-filter="{ mustSendFinalVersion: true, operationStatuses: [] }"
                  >
                  </NumberCardDashboardComponent>
                  <NumberCardDashboardComponent
                    trace-id="operations-after-sales-service-to-process"
                    class="flex-grow-1 flex-basis-0 pa-4"
                    title="SAV à programmer"
                    tooltip="Documents du SAV à transmettre aux opérationnels (équipe travaux) pour remise en conformité du chantier et renvoi du SAV signé par le client à la cellule Contrôle National du SAV + justificatifs éventuels"
                    :targeted-value="0"
                    :disabled="!filter.stepIds?.every((it) => it === 70)"
                    :value="
                      (v) =>
                        operationApi
                          .getSummary({ ...v, afterSalesServiceStatuses: ['SENT_BY_CONTROL_OFFICE'] })
                          .then((it) => it.data.operationsNumber)
                    "
                    :request-filter="{ afterSalesServiceStatuses: ['SENT_BY_CONTROL_OFFICE'] }"
                  >
                  </NumberCardDashboardComponent>
                  <!-- <VCol>
                      <NumberCardDashboardComponent title="SAV à traiter" :value="59" />
                    </VCol> -->
                </div>
                <!-- <VRow class="d-flex align-stretch gap-4"> -->
                <VRow class="d-flex align-stretch">
                  <VCol cols="7">
                    <!-- <VCol cols="7" style="flex-grow: 7; flex-basis: 0; max-width: 100%; flex-shrink: 0;"> -->
                    <NumberCardDashboardComponent
                      class="w-100"
                      title="Volume d'opérations en cours par étape"
                      :value="generatorQuantityData"
                    >
                      <template #after-title>
                        <VBtnToggle
                          v-model="barChartType"
                          density="compact"
                          variant="outlined"
                          mandatory
                          border="md"
                          color="primary"
                        >
                          <VBtn value="quantity"> Quantité </VBtn>
                          <VBtn value="kwhc"> kWhc </VBtn>
                          <VBtn value="euro"> Euros € </VBtn>
                        </VBtnToggle>
                      </template>
                      <template #value="{ value, loading }">
                        <div class="mt-2 d-flex align-center">
                          <div style="color: #60798b">
                            {{
                              barChartType === 'kwhc'
                                ? 'en kWhc'
                                : barChartType === 'euro'
                                  ? 'En euros (marge nette CEE)'
                                  : "Nombres d'opérations"
                            }}
                          </div>

                          <VSpacer />

                          <div :class="{ 'v-hidden': !hasStepFilter }" class="d-flex justify-end">
                            <VBtn
                              color="primary"
                              variant="text"
                              density="compact"
                              class="rounded-0"
                              prepend-icon="mdi-close"
                              @click="((filter.stepIds = []), trace('barchart_selection_changed', { action: 'clear' }))"
                              >Effacer la sélection</VBtn
                            >
                          </div>
                        </div>
                        <VChart
                          ref="barChart"
                          :loading="loading && globalLoading"
                          class="chart mt-4"
                          :option="generateBarChartsOptions(value)"
                          style="height: calc(340px - 30px)"
                          autoresize
                          @click="clickBarChartElement"
                        />
                      </template>
                    </NumberCardDashboardComponent>
                  </VCol>
                  <VCol cols="5">
                    <!-- <VCol style="flex-grow: 5; flex-basis: 0; max-width: 100%; flex-shrink: 0;"> -->
                    <NumberCardDashboardComponent
                      class="w-100"
                      title="Dates de fin de travaux prévisionnelles dépassées"
                      :value="generatorDelayedEstimatedEndWorksData"
                      :disabled="isDelayedWorksDisabled"
                    >
                      <template #title>
                        <VIcon color="#DB3735" class="me-2" size="20">mdi-clock-outline</VIcon>Dates de fin de travaux
                        prévisionnelles dépassées
                      </template>
                      <template #value="{ value, loading }">
                        <div class="d-flex mt-3 align-center">
                          <div style="color: #60798b">
                            {{ `Sur les étapes: ${filter.stepIds?.length ? filter.stepIds.join(', ') : 'Toutes'}` }}
                          </div>
                          <VSpacer />

                          <div :class="{ 'v-hidden': !hasDelayedWorksFilter }" class="d-flex justify-end">
                            <VBtn
                              color="primary"
                              variant="text"
                              density="compact"
                              class="rounded-0"
                              prepend-icon="mdi-close"
                              @click="onClearDelayDoughnut"
                              >Effacer la sélection</VBtn
                            >
                          </div>
                        </div>
                        <VChart
                          ref="doughnutChart"
                          :loading="loading && globalLoading"
                          :option="generateDoughnutsOptions(value, isDelayedWorksDisabled && !hasDelayedWorksFilter)"
                          style="height: calc(332px - 30px + 29px); width: 100%"
                          autoresize
                          @selectchanged="onDoughnutsSelectionChanged"
                          @legendselectchanged="onDoughnutsLegendSelectionChanged"
                          @click="clickDoughnutElement"
                        />
                      </template>
                    </NumberCardDashboardComponent>
                  </VCol>
                </VRow>
              </div>
            </VCol>
          </VRow>
        </VCol>
        <VCol class="d-flex flex-column">
          <div class="d-flex align-center mb-2 mt-4">
            <VIcon icon="mdi-format-list-bulleted" size="24" class="me-4" color="#768C9B"></VIcon>
            <div
              style="
                color: var(--Neutral-colors-Gray800, #314452);

                /* Heading/Heading XS */
                font-family: var(--nj-semantic-font-family-default);
                font-size: 1.25rem;
                font-style: normal;
                font-weight: 700;
                line-height: 28px;
                letter-spacing: 0;
                color: #314452;
              "
            >
              Liste des opérations
            </div>
            <div class="ml-2" style="font-size: 1.25rem">
              ({{ formatNumber(data.value?.totalElements ?? 0) }} résultats)
            </div>
            <VSpacer></VSpacer>
            <div class="d-flex gap-8">
              <template v-if="userStore.hasRole('ADMIN_PLUS')">
                <VLink color="primary" variant="text" @click="batchUpdateDialog = true"> MAJ en masse </VLink>
                <VLink
                  color="primary"
                  variant="text"
                  :disabled="disableChangeEntity"
                  icon="mdi-domain"
                  @click="changeEntityDialog = true"
                  >Changer d'organisation
                </VLink>
                <VLink
                  color="primary"
                  variant="text"
                  :loading="exportOperationsLoading"
                  icon="mdi-notification-clear-all"
                  @click="purgeOperations"
                  >Purger les opérations
                </VLink>
              </template>
              <VLink
                color="primary"
                variant="text"
                :loading="exportOperationsLoading"
                icon="mdi-tray-arrow-down"
                @click="exportOperations"
                >Exporter</VLink
              >
              <VLink color="primary" variant="text" icon="mdi-star-outline" @click="handleColumnManager"
                >Personnaliser</VLink
              >
            </div>
          </div>
          <div class="flex-grow-1 flex-basis-0">
            <OperationTable
              v-model:selection="selection"
              v-model:drawer="drawer"
              :pageable="pageable"
              :update-pageable="updatePageable"
              :data="data.value!"
              :valuations="filter.valuationMode ? valuations : undefined"
              :fixed="arrayExpanded"
              @update:drawer="(event, id) => (id ? getOperationDetail(id) : undefined)"
            />
          </div>
        </VCol>
        <FilterDrawer
          v-model:original-filter="filter"
          v-model:valuation-mode="filter.valuationMode"
          :model-value="drawer === 'filter'"
          :count="filterCount"
          @update:model-value="drawer = $event ? 'filter' : undefined"
        />
      </VRow>

      <CardDialog v-model="batchUpdateDialog" :disabled="batchUpdating" closable>
        <template #title> Modification en masse des données </template>

        <template #default>
          <VRow class="flex-column">
            <VCol>
              <VAlert type="warning">
                Ne modifiez en masse que si vous êtes sur de ce que vous faites !
                <div v-if="selection.length > 0">
                  Vous allez modifier seulement les éléments sélectionnées (soit un total de
                  {{ selection.length }} opérations)
                </div>
                <div v-else>
                  Vous allez modifier tous les élements filtrées (soit un total de
                  {{ data.value?.totalElements }} opérations)
                </div>
              </VAlert>
            </VCol>
            <VCol>
              <NjDisplayValue label="Valorisation">
                <template #value>
                  <div class="w-50">
                    <VSelect
                      v-model="batchUpdateRequest.valuationTypeId"
                      label="Type"
                      :items="valuationTypesStore.activeValuationTypes"
                      item-title="name"
                      item-value="id"
                      clearable
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
            <VCol>
              <NjDisplayValue label="Instructreur">
                <template #value>
                  <div class="w-50">
                    <RemoteAutoComplete
                      v-model="batchUpdateRequest.instructorId"
                      label="Instructeur"
                      :query-for-one="(id) => userApi.getOne(id)"
                      :query-for-all="
                        (s, pageable) =>
                          userApi.getAll(pageable, {
                            search: s,
                            roles: ['INSTRUCTEUR'] as ProfileType[],
                            active: true,
                          })
                      "
                      :item-title="(item: User) => displayFullnameUser(item)"
                      :rules="[requiredRule]"
                      infinite-scroll
                    />
                  </div>
                </template>
              </NjDisplayValue>
            </VCol>
          </VRow>
        </template>

        <template #actions="{}">
          <div v-if="batchUpdating">
            Opérations traités {{ batchUpdateCurrentIndex }}/{{ selection.length || data.value?.totalElements }}
          </div>
          <NjBtn :disabled="!batchUpdating" @click="batchCancelled = true">Annuler</NjBtn>
          <NjBtn :loading="batchUpdating" @click="batchUpdate">Valider</NjBtn>
        </template>
      </CardDialog>
      <AlertDialog
        v-model="batchUpdateResultDialog"
        :negative-button="false"
        @click:positive="batchUpdateResultDialog = false"
      >
        <VAlert v-if="batchUpdatingError" type="error">{{ batchUpdatingError }}</VAlert>
        <div>
          Opérations mises à jour ({{ batchUpdateResults.length }} opération(s) mises à jour)
          <template v-if="batchUpdateErrorOperationIds.length > 0">
            ({{ batchUpdateErrorOperationIds.length }} opérations en erreur)</template
          >:
        </div>
        <ul>
          <li v-for="r in batchUpdateResults" :key="r[0]">
            <RouterLink :to="{ name: 'OperationOneView', params: { id: r[0] } }" targer="_blank">{{ r[1] }}</RouterLink>
          </li>
        </ul>

        <template v-if="batchUpdateErrorOperationIds.length > 0">
          <p>Opérations en erreur :</p>
          <ul>
            <li v-for="r in batchUpdateErrorOperationIds" :key="r[0]">
              <RouterLink :to="{ name: 'OperationOneView', params: { id: r[0] } }" targer="_blank"
                >{{ r[1] }}(étape {{ r[2] }})</RouterLink
              >
            </li>
          </ul>
        </template>
      </AlertDialog>
    </template>
    <template #drawer>
      <OperationDrawer
        ref="operationDrawerRef"
        :model-value="drawer === 'detail'"
        :operation="selectedOperation.value"
        :loading="selectedOperation.loading"
        @update:model-value="drawer = $event ? 'detail' : undefined"
        @update:operation="onUpdateOperation"
        @final-version-sent="reloadData"
      />
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { beneficiaryApi } from '@/api/beneficiary'
import { entityApi } from '@/api/entity'
import { operationApi, type OperationDelayType, type OperationFilter } from '@/api/operation'
import { userApi } from '@/api/user'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import VLink from '@/components/VLink.vue'
import { useDialogStore } from '@/stores/dialog'
import { usePeriodsStore } from '@/stores/periods'
import { useSnackbarStore } from '@/stores/snackbar'
import { useStepsStore } from '@/stores/steps'
import { useUserStore } from '@/stores/user'
import { useValuationTypesStore } from '@/stores/valuationTypes'
import { controlOrderNatureLabel, type ControlOrderNature } from '@/types/calcul/standardizedOperationSheet'
import type { Entity } from '@/types/entity'
import { formatKiloNumber, formatNumber } from '@/types/format'
import { type Operation, type OperationRequest } from '@/types/operation'
import type { OperationSummaryByDelayDto, OperationSummaryByStepDto } from '@/types/operationSummary'
import { usePaginationInQuery } from '@/types/pagination'
import { requiredRule } from '@/types/rule'
import { displayFullnameUser, userHasRole, type ProfileType, type User } from '@/types/user'
import type { Valuation } from '@/types/valuation'
import { useElementBounding } from '@vueuse/core'
import type { AxiosPromise } from 'axios'
import { BarChart, PieChart } from 'echarts/charts'
import {
  GraphicComponent,
  GridComponent,
  LegendComponent,
  MarkAreaComponent,
  MarkLineComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import type { EChartsOption } from 'echarts/types/dist/shared'
import { cloneDeep, debounce, isArray, isBoolean, isEmpty, isEqual, keyBy, keys } from 'lodash'
import VChart from 'vue-echarts'
import type { LocationQuery } from 'vue-router'
import { dashboardDataKey } from '../dashboard/keys'
import NumberCardDashboardComponent from '../dashboard/NumberCardDashboardComponent.vue'
import DoublonDialog from '../DoublonDialog.vue'
import { useExportOperation } from '../exportOperation'
import EntityFilterDialog from './dialog/EntityFilterDialog.vue'
import FilterDrawer from './FilterDrawer.vue'
import OperationDrawer from './OperationDrawer.vue'
import OperationTable from './OperationTable.vue'
import { trace } from '@/stores/analytics'

const stepStore = useStepsStore()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()
const valuationTypesStore = useValuationTypesStore()
const periodsStore = usePeriodsStore()
const selection = ref<Operation[]>([])

const defaultFilter = {
  ...makeEmptyFilter(),
  operationStatuses: ['DOING'],
} as OperationFilter

const filter = ref<OperationFilter>(cloneDeep(defaultFilter))
const defaultOrder = userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR')
  ? ['statusRank,ASC', 'lastValidatedStepDateTime,DESC']
  : ['statusRank,ASC', 'chronoCode,DESC']

/**
 * Part Dashboard - START
 */
const globalLoading = ref(false)
const dashboardFilter = ref<OperationFilter>({})
provide(dashboardDataKey, {
  filter,
  loading: globalLoading,
  dashboardFilter,
})

const valuations = ref<Valuation[][]>([[]])
use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  BarChart,
  MarkAreaComponent,
  MarkLineComponent,

  GridComponent,

  GraphicComponent,
])
const barChart = useTemplateRef('barChart')
const barChartBounding = useElementBounding(barChart)
const doughnutChart = useTemplateRef('doughnutChart')
const doughnutChartBounding = useElementBounding(doughnutChart)
const onClearDelayDoughnut = () => {
  trace('delay_doughnut_selection_changed', { action: 'clear' })
  filter.value.delayTypes = undefined
}
const generateDoughnutsOptions = (
  data: OperationSummaryByDelayDto[],
  isDelayedWorksDisabled: boolean
): EChartsOption => {
  const isEmpty = data?.every((it) => it.operationsNumber === 0) ?? true
  const onlyOneSection = data?.reduce((acc, v) => acc + (v.operationsNumber > 0 ? 1 : 0), 0) === 1

  const keyedData = keyBy(data, 'delayMonths')
  const newData: { value: number | undefined; name: string; itemStyle: any; selected?: boolean }[] = [
    {
      value: keyedData[0]?.operationsNumber,
      name: 'Depuis < 3 mois',
      itemStyle: { color: '#FFEAE5' /* light/red/red-200 */, borderColor: '#FF8979', borderWidth: 2 },
      selected: filter.value.delayTypes?.includes('LESS_3_MONTHS') && filter.value.delayTypes?.length !== 4,
    },
    {
      value: keyedData[3]?.operationsNumber,
      name: 'Depuis > 3 mois',
      itemStyle: { color: '#FF8979' /* light/red/red-400 */, borderColor: '#FF8979', borderWidth: 2 },
      selected: filter.value.delayTypes?.includes('BETWEEN_3_AND_8_MONTHS') && filter.value.delayTypes?.length !== 4,
    },
    {
      value: keyedData[8]?.operationsNumber,
      name: 'Depuis > 8 mois',
      itemStyle: { color: '#DB3735' /* light/red/red-600 */, borderColor: '#DB3735', borderWidth: 2 },
      selected: filter.value.delayTypes?.includes('BETWEEN_8_AND_10_MONTHS') && filter.value.delayTypes?.length !== 4,
    },
    {
      value: keyedData[10]?.operationsNumber,
      name: 'Depuis > 10 mois',
      itemStyle: { color: '#7F1D1B' /* light/red/red-800 */, borderColor: '#7F1D1B', borderWidth: 2 },
      selected: filter.value.delayTypes?.includes('BETWEEN_10_AND_12_MONTHS') && filter.value.delayTypes?.length !== 4,
    },
  ]
    .reverse()
    .concat(
      data && !isEmpty
        ? []
        : [
            {
              value: 0,
              name: 'Aucune donnée',
              itemStyle: { color: '#CCCCCC' /* light/red/red-700 */ },
            } as any,
          ]
    )

  const total = data?.reduce((acc, v) => acc + v.operationsNumber, 0) ?? 0
  const isGlobalSelected = filter.value.delayTypes?.length === 4
  return {
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: '#333333',
      textStyle: {
        color: '#FFFFFF',
      },
      borderColor: '#333333',
    },
    legend: {
      data: newData.slice(0, 4).map((it) => it.name),
      orient: 'vertical',
      left: '60%',
      top: 'center',
      itemGap: 20,
      selected: newData
        .slice(0, 4)
        .map((it) => it.name)
        .reduce((acc, v) => ({ ...acc, [v]: true }), {}),
      // selectedMode: false,
      // data: ['Depuis > 10 mois', 'Depuis > 8 mois', 'Depuis > 3 mois', 'Depuis < 3 mois'],
      // left: 'left',
      // selectedMode: false,
      // value: newData.map((it) => ({
      //   name: it.name,
      //   itemStyle: it.itemStyle,
      // })),
      formatter: (arg1: string) => {
        return `{legend|${arg1}} : {bold|${keyedData?.[arg1 === 'Depuis > 10 mois' ? 10 : arg1 === 'Depuis > 8 mois' ? 8 : arg1 === 'Depuis > 3 mois' ? 3 : 0]?.operationsNumber ?? 0}}`
      },
      textStyle: {
        rich: {
          bold: {
            fontWeight: 'bold',
            fontSize: 14,
          },
          legend: {
            color: '#60798B',
            fontSize: 14,
          },
        },
      },
    } as any,
    series: [
      {
        type: 'pie',
        silent: isEmpty,
        dimensions: [],
        radius: ['40%', '60%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        selectedMode: isDelayedWorksDisabled || isEmpty ? false : 'multiple',
        selectedOffset: onlyOneSection ? 0 : 10,
        select: {
          itemStyle: {
            borderColor: '#007ACD',
            borderWidth: 4,
          },
        },
        emphasis: {
          // label: {
          //   show: true,
          //   fontSize: 40,
          //   fontWeight: 'bold'
          // }
          label: {},
        },
        labelLine: {
          show: true,
        },
        itemStyle: {
          borderWidth: 4,
        },
        data: newData,
      },
    ],
    graphic: [
      // {
      //   type: 'text',
      //   left: '25%',
      //   bottom: '50%',
      //   // bounding: 'raw',
      //   // position: ['25%', '50%'],
      //   origin: ['25%', '50%'],
      //   style: {
      //     text: 'Total',
      //     textAlign: 'center',
      //     textVerticalAlign: 'center',
      //     fontSize: 14,
      //     fill: '#999',
      //   },
      //   shape: {

      //   }
      // },
      // {
      //   type: 'text',
      //   left: '25%',
      //   top: '50%',
      //   bounding: 'raw',
      //   style: {
      //     text: '3,147',
      //     textAlign: 'middle',
      //     textVerticalAlign: 'middle',

      //     fontSize: 24,
      //     fontWeight: 'bold',
      //     fill: '#333',
      //   },
      // },
      {
        type: 'circle',
        // left: '25%',
        top: 'middle',
        silent: true,
        shape: {
          cx: doughnutChartBounding.width.value * 0.3,
          cy: 0,
          r: doughnutChartBounding.height.value * 0.2,
        },
        style: {
          fill: isGlobalSelected ? '#E7EEFC' : 'transparent',
        },
      },
      {
        type: 'group',
        left: '30%',
        top: '50%',
        // bounding: 'raw',
        silent: true,
        textContent: {
          type: 'text',
          style: {
            text: `{title|Total}\n\n{${isGlobalSelected ? 'selectedValue' : 'value'}|${total}}`,
            align: 'center',
            fill: '#000',
            rich: {
              title: {
                fontSize: 16,
                fill: '#60798B',
                stroke: '#60798B',
              },
              value: {
                fontSize: 26,
                fontWeight: 'bold',
                fill: '#171D21',
                stroke: '#171D21',
              },
              selectedValue: {
                fontSize: 26,
                fontWeight: 'bold',
                fill: '#007ACD',
                stroke: '#007ACD',
              },
            },
          },
        },
        textConfig: {
          position: 'inside',
          // align: 'center',
          // verticalAlign: 'middle',
        },
      },
      {
        type: 'circle',
        // left: '25%',
        top: 'middle',
        shape: {
          cx: doughnutChartBounding.width.value * 0.3,
          cy: 0,
          r: doughnutChartBounding.height.value * 0.1865,
        },
        style: {
          fill: 'transparent',
          stroke: isGlobalSelected ? '#007ACD' : undefined,
          // stroke: '#007ACD',
          lineWidth: 5,
        },
        z: 1,
        silent: isEmpty,
        onclick: () => {
          onTotalDoughnutCircleClick()
        },
      },
    ],
  }
}

const onTotalDoughnutCircleClick = () => {
  if (isDelayedWorksDisabled.value && !hasDelayedWorksFilter.value) {
    return
  }
  selectDounghutSector(filter.value.delayTypes?.length !== 4 ? operationDelayTypes.concat() : [])
}

const generatorDelayedEstimatedEndWorksData = (v: OperationFilter) => {
  return operationApi
    .getSummaryGroupedByDelay({
      ...v,
      delayTypes: [],
    })
    .then((it) => it.data)
}
const stepIdsToDisplayInBarchart = [20, 30, 40, 50, 60, 70, 80, 90, 100]
const generatorQuantityData = (v: OperationFilter) => {
  return operationApi
    .getSummaryGroupedByStep({
      ...v,
      // minEstimatedEndWorksDate: undefined,
      // maxEstimatedEndWorksDate: formatLocalDate(add(new Date(), { months: -3 })),
      stepIds: [],
    })
    .then((it) => it.data)
}

const barChartType = ref<'kwhc' | 'quantity' | 'euro'>('quantity')
const generateBarChartsOptions = (data2: OperationSummaryByStepDto[]): any => {
  const successRawData: Record<number, { cumac?: number; count?: number; netMargin?: number }> = {}
  const delayedRawData: Record<number, { cumac?: number; count?: number; netMargin?: number }> = {}
  data2?.forEach((it) => {
    const data = !it.isDelayed ? successRawData : delayedRawData
    if (!data[it.stepId]) {
      data[it.stepId] = {}
    }

    data[it.stepId].cumac = it.classicCumacSum + it.precariousnessCumacSum
    data[it.stepId].count = it.operationsNumber
    data[it.stepId].netMargin = it.netMarginSum
  })
  const errorData =
    stepIdsToDisplayInBarchart.map((stepId) => ({
      value:
        (barChartType.value === 'kwhc'
          ? delayedRawData[stepId]?.cumac
          : barChartType.value === 'euro'
            ? delayedRawData[stepId]?.netMargin
            : delayedRawData[stepId]?.count) ?? 0,
      itemStyle: {
        color: '#F34F46',
      },
    })) ?? []

  const operationData =
    stepIdsToDisplayInBarchart.map((stepId) => ({
      value:
        (barChartType.value === 'kwhc'
          ? successRawData[stepId]?.cumac
          : barChartType.value === 'euro'
            ? successRawData[stepId]?.netMargin
            : successRawData[stepId]?.count) ?? 0,
      itemStyle: {
        color: '#7FADEF ',
      },
    })) ?? []

  const borderData =
    stepIdsToDisplayInBarchart.map((stepId) => ({
      value:
        barChartType.value === 'kwhc'
          ? (successRawData[stepId]?.cumac ?? 0) + (delayedRawData[stepId]?.cumac ?? 0)
          : barChartType.value === 'euro'
            ? (successRawData[stepId]?.netMargin ?? 0) + (delayedRawData[stepId]?.netMargin ?? 0)
            : (successRawData[stepId]?.count ?? 0) + (delayedRawData[stepId]?.count ?? 0),
      itemStyle: {
        color: filter.value.stepIds?.length && filter.value.stepIds.includes(stepId) ? '#FFF8' : 'transparent',
        borderColor: '#007ACD',
        borderWidth: filter.value.stepIds?.length && filter.value.stepIds.includes(stepId) ? 4 : 0,
      },
    })) ?? []

  const barWidth = '30%'
  const series = [
    {
      name: 'Total',
      type: 'bar',
      barWidth,
      label: {
        show: false,
      },
      data: borderData,
      // stack: 'total',
      itemStyle: {
        color: '#transparent',
      },
      barGap: '-100%', // Overlap other bars
      emphasis: {
        disabled: true, // Don't trigger tooltip
      },
      tooltip: {
        show: false,
      },
      z: 100,
    },
    {
      name: 'Autres opérations en cours',
      type: 'bar',
      barWidth,
      label: {
        show: false,
      },
      data: operationData,
      stack: 'total',
      itemStyle: {
        color: '#7FADEF',
      },
    },
    {
      name: 'Date de fin des travaux prévisionnelle dépassée',
      type: 'bar',
      stack: 'total',
      barWidth,
      label: {
        show: true,
        position: 'top',
        formatter: (params: any): string => {
          const value = borderData[params.dataIndex].value

          let styleKey = 'default'

          if (filter.value.stepIds?.length) {
            if (filter.value.stepIds.includes(stepIdsToDisplayInBarchart[params.dataIndex])) {
              styleKey = 'selected'
            }
          }

          let v = ''
          if (barChartType.value === 'kwhc') {
            if (value >= 1000000) {
              v = formatNaturalNumber(value / 1000000) + ' G'
            } else if (value >= 1000) {
              v = formatNaturalNumber(value / 1000) + ' M'
            } else {
              v = formatNaturalNumber(value) + ' k'
            }
          } else if (barChartType.value === 'euro') {
            v = formatKiloPriceNumber(value) ?? ''
          } else {
            v = formatNaturalNumber(value) ?? ''
          }

          return `{${styleKey}|${v}}`
        },
        rich: {
          default: {},
          selected: {
            // fontWeight: 'bold',
            color: '#005AA0',
          },
        },
      },
      data: errorData,
      itemStyle: {
        color: '#DB3735',
      },
      aeraStyle: {},
      // markArea: {
      //   silent: true,
      //   itemStyle: {
      //     color: '#F6F8FD',
      //     // opacity: 0.5,
      //   },
      //   data: [
      //     [
      //       {
      //         // name: 'Fond pour les DR',
      //         xAxis: '20',
      //       },
      //       {
      //         xAxis: '50',
      //       },
      //     ],
      //   ],
      // },
    },
    // {
    //   name: 'Fin de travaux dépassée depuis au moins 3 mois',
    //   type: 'bar',
    //   stack: 'total',
    //   data: errorData,
    //   itemStyle: {
    //     color: '#F34F46',
    //   },
    // },
  ]
  return {
    grid: {
      top: 45,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
        // z: 0,
        // triggerEmphasis: false,
        // shadowStyle: {
        //   color: 'rgba(150, 150, 150, .5)',
        // },
      },
      backgroundColor: '#333333',
      textStyle: {
        color: '#FFFFFF',
      },
      borderWitdh: 0,
      borderColor: '#333333',

      valueFormatter: (params: any) => {
        const value = params as number
        if (barChartType.value === 'kwhc') {
          return formatKiloNumber(value) + 'Whc'
        }
        if (barChartType.value === 'euro') {
          return formatKiloPriceNumber(value) ?? ''
        }

        return formatNumber(value)!
      },
      confine: true,
      // triggerOn: 'click',
    },
    // axisPointer: {
    //   // triggerOn: 'click',
    //   triggerEmphasis: false,
    // },
    legend: {
      // show: true,
      selectedMode: false,
      bottom: '0%',
      left: 'center',
      padding: 0,
      itemGap: 20,
      data: ['Date de fin des travaux prévisionnelle dépassée', 'Autres opérations en cours'],
      formatter: (name: string) => {
        return (
          '{legend|' +
          name +
          '}' +
          ' : {bold|' +
          (barChartType.value === 'kwhc'
            ? formatKiloNumber
            : barChartType.value === 'euro'
              ? formatKiloPriceNumber
              : formatNumber)(
            (name === 'Date de fin des travaux prévisionnelle dépassée' ? errorData : operationData).reduce(
              (acc, v) => acc + v.value,
              0
            )
          ) +
          '}'
        )
      },
      textStyle: {
        rich: {
          bold: {
            fontWeight: 'bold',
            fontSize: 14,
          },
          legend: {
            color: '#60798B',
            fontSize: 14,
          },
        },
      },
    },
    xAxis: {
      type: 'category',
      data: [
        { value: '20', itemStyle: {} },
        { value: '30', itemStyle: {} },
        { value: '40', itemStyle: {} },
        { value: '50', itemStyle: {} },
        { value: '60', itemStyle: {} },
        { value: '70', itemStyle: {} },
        { value: '80', itemStyle: {} },
        { value: '90', itemStyle: {} },
        { value: '100', itemStyle: {} },
      ],
      // axisTick: {
      // alignWithLabel: false,
      // interval: 0,
      // },
      axisTick: {},
      axisLabel: {
        interval: 0,
        formatter: (value: any) => {
          let styleKey = 'default'
          if (filter.value.stepIds?.length) {
            if (!filter.value.stepIds.includes(parseInt(value))) {
              // styleKey = 'disabled'
            } else {
              styleKey = 'selected'
            }
          }

          return `{${styleKey}|${value}${styleKey === 'selected' ? ' ✖' : ''}}`
        },
        rich: {
          disabled: {
            backgroundColor: '#E7EEFC66',
            borderColor: '#7FADEF66',
            borderWidth: 1,
            padding: 4,
            color: '#00000066',
          },
          default: {
            backgroundColor: '#E7EEFC',
            borderColor: '#7FADEF',
            borderWidth: 1,
            padding: 4,
            color: '#000000',
          },
          selected: {
            backgroundColor: '#E7EEFC',
            borderColor: '#7FADEF',
            borderWidth: 2,
            padding: 4,
            color: '#000000',
            fontWeight: 'bold',
          },
        },
      },
      // axisLine: { onZero: false },
      tooltip: {
        show: true,
        formatter: (v: any) => {
          const stepId = v.value
          return `${stepId}<br />${stepStore.stepsMap[stepId].name}`
        },
        extraCssText: 'text-align:center;',
      },
      triggerEvent: true,
    },
    graphic: [
      {
        type: 'rect',
        left: barChartBounding.width.value * 0.1,
        top: 0,
        shape: {
          width: barChartBounding.width.value * 0.356,
          height: barChartBounding.height.value * 0.78,
        },
        style: {
          fill: '#F6F8FD',
        },
        z: -1,
      },
      {
        type: 'text',
        left: '12%',
        top: 10,
        silent: true,
        // bottom: 0,
        style: {
          text: `Côté DR : ${(barChartType.value === 'kwhc' ? formatKiloNumber : barChartType.value === 'euro' ? formatKiloPriceNumber : formatNumber)(borderData.slice(0, 4).reduce((acc, v) => acc + v.value, 0))}`,
          font: '18px sans-serif',
          fill: '#005AA0',
          textAlign: 'center',
        },
        z: 1000,
      },
      {
        type: 'text',
        left: '48%',
        top: 10,
        silent: true,
        style: {
          text: `Côté siège : ${(barChartType.value === 'kwhc' ? formatKiloNumber : barChartType.value === 'euro' ? formatKiloPriceNumber : formatNumber)(borderData.slice(4, 10).reduce((acc, v) => acc + v.value, 0))}`,
          font: '18px sans-serif',
          fill: '#60798B',
          textAlign: 'center',
        },
        z: 1000,
      },
      {
        type: 'line',
        silent: true,
        shape: {
          x1: 0,
          y1: 0,
          x2: 0,
          y2: 270, // adjust based on your chart height
        },
        style: {
          stroke: '#7FADEF',
          lineDash: [5, 5],
          lineWidth: 1,
        },
        // We'll position this with the `position` and `z`
        // Calculate horizontal position between Wed (index 2) and Thu (index 3)
        position: [0, 0],
        left: '45.3%',
        top: 0,
        z: 10,
      },
    ],
    yAxis: {
      type: 'value',
      // name: barChartType.value === 'kwhc' ? 'Whc' : 'Nb. opérations',
      axisLabel: {
        formatter: (params: any) => {
          const value = params
          if (barChartType.value === 'kwhc') {
            return formatKiloNumber(value)
          }
          if (barChartType.value === 'euro') {
            return formatKiloPriceNumber(value) ?? ''
          }
          return value.toLocaleString()
        },
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: true,
        // lineStyle: {
        //   color: '#333', // customize color
        //   width: 2       // thicker line for emphasis
        // }
      },
    },
    series,
  }
}

const selectedEntities = ref<Entity[]>([])
const updateSelectedEntities = (entities: Entity[]) => {
  filter.value.entityNavFullIds = entities.map((it) => it.navFullId)
}
watch(
  () => filter.value.entityNavFullIds,
  (v) => {
    if (
      !isEqual(
        v,
        selectedEntities.value.map((it) => it.navFullId)
      )
    ) {
      if (v?.length) {
        const ids = v.map((navFullId) => navFullId.substring(navFullId.length - 3, navFullId.length))
        entityApi
          .getAll(
            {
              ids: ids,
            },
            {}
          )
          .then((response) => (selectedEntities.value = response.data.content))
      } else {
        selectedEntities.value = []
      }
    }
  }
)
const selectedEntity = ref<string>('')
const changeEntityLoading = ref(false)
const changeEntity = () => {
  changeEntityLoading.value = true
  let operationFilter
  if (selection.value.length == 0) {
    operationFilter = filter.value
  } else {
    operationFilter = { operationIds: selection.value.map((item) => item.id) }
  }
  operationApi
    .changeEntity(operationFilter, { entityId: selectedEntity.value })
    .then((response) => {
      snackbarStore.setSuccess(`${response.data} opérations ont changé d'organisation`)
      reload()
      selection.value = []
    })
    .catch(async (err) => snackbarStore.setError(await handleAxiosException(err)))
    .finally(() => {
      changeEntityDialog.value = false
      changeEntityLoading.value = false
    })
}

watch(dashboardFilter, () => {
  reload()
})

const hasStepFilter = computed(() => {
  return !!filter.value.stepIds?.length && filter.value.stepIds.some((it) => it >= 20 && it <= 100)
})
const hasDelayedWorksFilter = computed(() => {
  return filter.value.delayTypes?.length
})
const isDelayedWorksDisabled = computed(() => {
  return (
    !hasDelayedWorksFilter.value &&
    (!!filter.value.minEndWorksDate ||
      !!filter.value.maxEndWorksDate ||
      !!filter.value.maxEstimatedEndWorksDate ||
      !!filter.value.minEstimatedEndWorksDate ||
      (!isEqual(filter.value.operationStatuses, []) && !isEqual(filter.value.operationStatuses, ['DOING'])))
  )
})

const clickBarChartElement = (event: any) => {
  const stepId: number = event.componentType === 'xAxis' ? Number(event.value) : Number(event.name)
  filter.value = {
    ...filter.value,
    stepIds: filter.value.stepIds
      ? filter.value.stepIds.includes(stepId)
        ? filter.value.stepIds.filter((it) => it !== stepId)
        : filter.value.stepIds.concat(stepId)
      : [stepId],
    operationStatuses: ['DOING'],
  }
  trace('barchart_selection_changed', {
    [event.componentSubType === 'bar' ? 'clickedBar' : 'clickedLegend']: stepId,
    selectedStepIds: filter.value.stepIds,
    action: filter.value.stepIds?.includes(stepId) ? 'select' : 'unselect',
  })
}

/*
 * Dashboard Block - END
 */
const queryMapper = (query: LocationQuery): Record<string, unknown> => ({
  ...query,
  territoryIds: mapQueryToTable(query.territoryIds, true),
  stepIds: mapQueryToTable(query.stepIds, true),
  periodIds: mapQueryToTable(query.periodIds, true),
  valuationTypeIds: mapQueryToTable(query.valuationTypeIds, true),
  operationStatuses: mapQueryToTable(query.operationStatuses, false),
  standardizedOperationSheetIds: mapQueryToTable(query.standardizedOperationSheetIds, true),
  entityNavFullIds: isArray(query.entityNavFullIds)
    ? query.entityNavFullIds
    : query.entityNavFullIds
      ? [query.entityNavFullIds]
      : [],
  emmyFolderIds: mapQueryToTable(query.emmyFolderIds, true),
  instructorIds: mapQueryToTable(query.instructorIds, true),
  controlOrderNatures: mapQueryToTable(query.controlOrderNatures, false),
})

const { data, pageFilter, pageable, updatePageable, updateFilter, reload } = usePaginationInQuery<
  Operation,
  OperationFilter
>(
  (filter, pageable) =>
    filter.valuationMode
      ? operationApi.findAllWithValuations({ ...filter, ...dashboardFilter.value }, pageable).then((response) => {
          valuations.value = response.data.content.map((it) => it.valuations)
          return Promise.resolve({
            ...response,
            data: {
              ...response.data,
              content: response.data.content.map((it) => it.operation),
            },
          })
        })
      : operationApi.findAll({ ...filter, ...dashboardFilter.value }, pageable),
  {
    defaultPageFilter: { ...filter.value },
    defaultPageablePartial: {
      page: 0,
      size: 50,
      sort: defaultOrder,
    },
    queryToFilterMapper: queryMapper,
    saveFiltersName: 'OperationAllView',
  }
)

const reloadData = () => {
  return Promise.all([
    reload(),
    selectedOperation.value.value
      ? handleAxiosPromise(selectedOperation, simulationApi.findById(selectedOperation.value.value.id), {
          afterError: () =>
            snackbarStore.setError(
              selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
            ),
        })
      : Promise.resolve(),
  ])
}
// Headers
const operationDrawerRef = ref<typeof OperationDrawer | null>(null)
const drawer = ref<'filter' | 'detail' | 'column' | undefined>()
const selectedOperation = ref(emptyValue<Operation>())
const getOperationDetail = async (id: number) => {
  operationDrawerRef.value!.check(async () => {
    await handleAxiosPromise(selectedOperation, simulationApi.findById(id), {
      afterError: () =>
        snackbarStore.setError(
          selectedOperation.value.error ?? "Une erreur est survenue lors de la récpération de l'opération"
        ),
    })
  })
}

const handleFilterDrawer = () => {
  drawer.value = drawer.value === 'filter' ? undefined : 'filter'
}

const handleColumnManager = () => {
  drawer.value = drawer.value === 'column' ? undefined : 'column'
}

const debounceFilter = debounce((v: any) => updateFilter(v), 300)

watch(
  filter,
  (v) => {
    if (!isEqual(v, pageFilter.value)) {
      debounceFilter(v)
    }
  },
  {
    deep: true,
  }
)

watch(
  () => pageFilter.value,
  (v) => {
    if (!isEqual(v, filter.value)) {
      const tempFilter = cloneDeep(v) as any
      Object.keys(tempFilter).forEach((key) => {
        if (isArray(tempFilter[key]) && key !== 'entityNavFullIds' && (tempFilter[key] as any[]).length !== 0) {
          ;(tempFilter[key] as any[]).forEach((val: string, index: number) => {
            if (!Number.isNaN(parseInt(val))) {
              ;(tempFilter[key] as any[])[index] = parseInt(val)
            }
          })
        }
      })
      filter.value = tempFilter as any
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// Search
const updateSearch = (v: string) => {
  const filter = {
    ...unref(pageFilter),
    search: v,
  }
  updateFilter(filter)
}

const onUpdateOperation = () => {
  reload()
}

const dialogStore = useDialogStore()

const disableChangeEntity = computed(() =>
  selection.value.length == 0
    ? (filter.value?.entityNavFullIds ?? []).length != 1
    : new Set(selection.value.map((i) => i.entity.id)).size > 1
)

const { exportOperationsLoading, exportOperations } = useExportOperation(pageFilter, selection, data, false)

const purgingOperations = ref(false)
const purgeOperations = async () => {
  if (!(pageFilter.value.stepIds?.every((it) => it >= 10 && it <= 40) ?? false)) {
    dialogStore.addAlert2({
      message: 'Vous devez sélectionner seulement des étapes entre 10 et 40 pour purger des opérations',
      title: 'Sélection étapes valides requises',
      maxWidth: '640px',
    })
    return
  }
  if (!isEqual(pageFilter.value.operationStatuses, ['DOING'])) {
    dialogStore.addAlert2({
      message: 'Vous ne pouvez purger seulement que sur les opérations en cours',
      title: 'Purge sur les opérations en cours seulement',
      maxWidth: '640px',
    })
    return
  }
  if ((pageFilter.value.minimumMonthsOld ?? 0) === 0) {
    dialogStore.addAlert2({
      message: "Vous devez saisir un minimum de durée de vétusté de l'opération",
      title: 'Nombre de mois requis',
      maxWidth: '640px',
    })
    return
  }

  if (
    await dialogStore.addAlert({
      message: "Vous allez purger un ensemble d'opérations. Confirmez vous bien votre action ?",
      title: 'Confirmation ',
      maxWidth: '640px',
    })
  ) {
    purgingOperations.value = true
    const allResponses = await Promise.allSettled<AxiosPromise[]>(
      data.value.value?.content.map((it) => operationApi.purge(it.id)) ?? []
    )
    const hasErrors = allResponses.some((it) => it.status === 'rejected' || it.value.status >= 400)
    if (hasErrors) {
      snackbarStore.setError("Certaines opérations n'ont pas pu être purgées.")
    } else {
      snackbarStore.setSuccess('Les opérations sélectionnées ont bien été purgées.')
    }
    purgingOperations.value = false
    reload()
  }
}

const changeEntityDialog = ref(false)

watch(changeEntityDialog, (v) => {
  if (v) {
    selectedEntity.value = ''
  }
})
const changeEntityMessage = computed(
  () => `
  Attention vous allez changer d'organisation ${
    selection.value.length ? selection.value.length : data.value.value?.totalElements
  } opérations.
  Vous ne pouvez changer des opérations d'organisation que si elles appartiennent à la même organisation.
  Si certaines opérations sont dans un regroupement, le regroupement et ses opérations changeront d'organisation.
`
)

const batchUpdateDialog = ref(false)
const batchUpdateResultDialog = ref(false)
const batchUpdating = ref(false)
const batchCancelled = ref(false)
const batchUpdatingError = ref<string>()
const batchUpdateRequest = ref<Partial<OperationRequest>>({})
const batchUpdateResults = shallowRef<[number, string][]>([])
const batchUpdateErrorOperationIds = shallowRef<[number, string, number][]>([])
const batchUpdateCurrentIndex = ref(0)
const batchUpdate = async () => {
  batchCancelled.value = false
  if (
    await dialogStore.addAlert({
      title: "Confirmation de modifications en masse d'opérations",
      message: "Vous allez faire une modification en masse d'opérations: êtes -vous certains de ce que vous faites ?",
      maxWidth: '640px',
    })
  ) {
    batchUpdating.value = true
    batchUpdatingError.value = undefined
    batchUpdateErrorOperationIds.value = []
    const totalElements = selection.value.length ? selection.value.length : (data.value.value?.totalElements ?? 0)
    const pageSize = 20
    const totalPages = Math.ceil(totalElements / pageSize)
    const results: [number, string][] = []
    batchUpdateCurrentIndex.value = 0
    const errorCount: typeof batchUpdateErrorOperationIds.value = []

    const filter = selection.value.length ? { operationIds: selection.value.map((it) => it.id) } : pageFilter.value

    try {
      for (let i = totalPages; i >= 0; i--) {
        if (batchCancelled.value) {
          break
        }
        const data = await operationApi
          .findAll(selection.value.length > 0 ? { operationIds: selection.value.map((it) => it.id) } : filter, {
            size: pageSize,
            page: i,
            sort: ['id'],
          })
          .catch((e) => {
            snackbarStore.setError('Erreur lors du chargement des opérations pour la MAJ en masse')
            throw e
          })
        for (const op of data.data.content) {
          if (batchCancelled.value) {
            break
          }
          const refOp = mapToOperationRequest(op)
          const currentOpRequest = { ...refOp, ...batchUpdateRequest.value }

          if (!isEqual(refOp, currentOpRequest)) {
            await simulationApi
              .batchUpdate(
                {
                  operationIds: [op.id],
                },
                batchUpdateRequest.value
              )
              .then(() => {
                results.push([op.id, op.chronoCode])
              })
              .catch(() => {
                errorCount.push([op.id, op.chronoCode, op.stepId])
              })
          }
          batchUpdateCurrentIndex.value++
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_: unknown) {
      batchUpdatingError.value = 'Erreur lors de la mise à jour de masse d operations'
    }
    batchUpdateResults.value = results
    batchUpdateResultDialog.value = true
    batchUpdateDialog.value = false
    batchUpdating.value = false
    batchUpdateErrorOperationIds.value = errorCount
    selection.value = []
    reload()

    // simulationApi
    //   .batchUpdate(filter, batchUpdateRequest.value)
    //   .then(() => {
    //     snackbarStore.setSuccess(
    //       `La mise à jour en masse est terminée. ${results.length} opérations ont été mises à jour avec succès${
    //         batchUpdateErrorOperationIds.value.length
    //           ? `, ${batchUpdateErrorOperationIds.value.length} n'ont pas pu être mises à jour`
    //           : ''
    //       }.`
    //     )
    //   })
    //   .catch(() => {
    //     batchUpdatingError.value = 'Erreur lors de la mise à jour de masse d operations'
    //     batchUpdating.value = false
    //   })
    //   .finally(() => {
    //     batchUpdateResults.value = results
    //     batchUpdateResultDialog.value = true
    //     batchUpdateDialog.value = false
    //     batchUpdating.value = false
    //     selection.value = []
    //     reload()
    //   })
  }
}
const arrayExpanded = ref(import.meta.env.VITE_FEATURE_TDB !== 'true')

const filterCount = computed((): number => {
  return (Object.keys(filter.value) as Array<keyof OperationFilter>).reduce<number>((acc, k): number => {
    let r: number = 0
    const value = filter.value[k]
    if (k === 'operationStatuses') {
      r = !isEqual(value, ['DOING']) && !isEqual(value, []) ? 1 : 0
    } else if (k === 'myRequests' || k === 'valuationMode' || k === 'warning') {
      r = value ? 1 : 0
    } else {
      r = value != null && (isBoolean(value) || !isEmpty(value)) ? 1 : 0
    }

    return acc + r
  }, 0)
})

const selectDounghutSector = (selectedDelayTypes: OperationDelayType[]) => {
  let semiFilter: OperationFilter = {}

  // if (dataIndex != null) {
  // const data = operationDelayTypes[dataIndex]
  semiFilter = {
    operationStatuses: ['DOING'],
    hasActualEndWorksDate: false,
    delayTypes: selectedDelayTypes,
  }
  // } else {
  //   semiFilter = {
  //     operationStatuses:
  //       !isEqual(baseFilter.operationStatuses, ['DOING']) && baseFilter.operationStatuses?.length
  //         ? ['DOING']
  //         : baseFilter.operationStatuses,
  //     hasActualEndWorksDate: null,
  //   }
  // }

  updateFilter({
    ...filter.value,
    ...semiFilter,
  })
  // dashboardFilter.value = {
  //   ...toRaw(dashboardFilter.value),
  //   ...semiFilter,
  // }
}
const onDoughnutsSelectionChanged = (a: any) => {
  if (isDelayedWorksDisabled.value && !hasDelayedWorksFilter.value) {
    return
  }
  const selectedDelayTypes =
    a.selected[0]?.dataIndex.map((i: number) => operationDelayTypes[operationDelayTypes.length - i - 1]) ?? []
  selectDounghutSector(selectedDelayTypes)
  trace('delay_doughnut_selection_changed', {
    clickedSector: operationDelayTypes[operationDelayTypes.length - 1 - a.fromActionPayload.dataIndexInside],
    selectedSectors: selectedDelayTypes,
    action: a.fromActionPayload.type,
  })
}

const clickDoughnutElement = (event: any) => {
  console.debug('clickDoughnutElement', event)
}

const onDoughnutsLegendSelectionChanged = (a: any) => {
  if (a.type === 'legendselectchanged') {
    let index: number
    switch (a.name) {
      case 'Depuis < 3 mois': {
        index = 3
        break
      }
      case 'Depuis > 3 mois': {
        index = 2
        break
      }
      case 'Depuis > 8 mois': {
        index = 1
        break
      }
      default: {
        index = 0
        break
      }
    }

    const delayType = operationDelayTypes[operationDelayTypes.length - 1 - index]

    const selectedDelayTypes = filter.value.delayTypes?.includes(delayType!)
      ? filter.value.delayTypes.filter((it) => it !== delayType)
      : [...(filter.value.delayTypes ?? []), delayType!]

    trace('delay_doughnut_selection_changed', {
      clickedLegend: delayType,
      selectedSectors: selectedDelayTypes,
      action: selectedDelayTypes.includes(delayType) ? 'select' : 'unselect',
    })

    doughnutChart.value?.setOption(
      {
        legend: {
          selected: {
            ...(doughnutChart.value!.getOption() as any).legend.selected,
            [a.name]: true,
          },
        },
      },
      false
    )
    selectDounghutSector(selectedDelayTypes)
  }
}

// Old - before tdb
const headerButtons = computed(() => {
  const res = []

  if (userStore.hasRole('ADMIN_PLUS')) {
    res.push({
      label: 'MAJ en masse',
      onClick: () => {
        batchUpdateDialog.value = true
      },
    })
    res.push({
      label: "Changer d'organisation",
      disabled: disableChangeEntity.value,
      onClick: () => {
        changeEntityDialog.value = true
      },
    })

    res.push({
      label: 'Purger les opérations',
      loading: exportOperationsLoading.value,
      onClick: purgeOperations,
    })
  }
  res.push({
    onClick: exportOperations,
    loading: exportOperationsLoading.value,
    label: 'Exporter',
    icon: 'mdi-tray-arrow-down',
  })
  res.push({
    label: 'Personnaliser',
    onClick: handleColumnManager,
    icon: 'mdi-star-outline',
  })
  return res
})
const showResetFilter = computed(() => {
  return !isEqual(filter.value, defaultFilter)
})

const hasFeatureTdb = ref(import.meta.env.VITE_FEATURE_TDB === 'true')

// Filtre Soumis à arrêté contrôle
const updateControlOrderNatures = (ev: (ControlOrderNature | 'NONE')[]) => {
  if (ev.includes('NONE') && controlOrderNatures.value.includes('NONE') !== true) {
    controlOrderNatures.value = ['NONE']
    // filter.value.noControlOrder = true
  } else {
    // filter.value.noControlOrder = undefined
    controlOrderNatures.value = ev.filter((it) => it !== 'NONE')
  }

  filter.value.noControlOrder = controlOrderNatures.value.includes('NONE')
  filter.value.controlOrderNatures = controlOrderNatures.value.filter((it) => it !== 'NONE') as ControlOrderNature[]
}

const controlOrderNatures = ref<(ControlOrderNature | 'NONE')[]>([])
const controlOrderNatureItems = (
  controlOrderNatureLabel as unknown as { value: ControlOrderNature | 'NONE'; label: string }[]
)
  .concat({
    value: 'NONE',
    label: 'Non',
  })
  .map((i) => ({
    title: i.label,
    value: i.value,
  }))

watch(barChartType, (v) => {
  trace('barchart_type_change', { type: v })
})
</script>
