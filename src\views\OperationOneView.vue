<template>
  <NjPage
    :title="
      operation.value?.id !== 0
        ? ((operation.value?.stepId === 0
            ? 'Simulation : ' + operation.value?.simulationName
            : operation.value?.operationName) ?? '') +
          ' - ' +
          (operation.value?.standardizedOperationSheet.operationCode ?? '')
        : operation.value?.standardizedOperationSheet.operationCode
    "
    :subtitle="operation.value?.standardizedOperationSheet.description"
    :can-go-back="canGoBack"
    :error-message="errorMessage || operation.error"
    expend-body
    v-bind="$attrs"
  >
    <template v-if="display.mdAndDown" #after-title>
      <OperationStepChip class="ms-4" :operation="operation.value" />
      <VDialog max-width="640px">
        <template #activator="{ props }">
          <NjIconBtn icon="mdi-history" v-bind="props" color="primary" variant="square-outline" class="ms-4" />
        </template>
        <template #default="{ isActive }">
          <VCard>
            <VCardTitle class="d-flex align-center py-0 pe-0">
              Historique des étapes
              <VSpacer />
              <NjIconBtn icon="mdi-close" class="rounded-0" @click="isActive.value = false" />
            </VCardTitle>
            <VDivider />
            <VCardText>
              <VList class="py-0">
                <VListItem v-for="stepId in steps" :key="stepId">
                  <template #prepend>
                    <!-- <div class="h-100 background-error" style="width:32px"></div> -->
                    <VIcon
                      :icon="stepId === operation.value?.stepId ? 'mdi-circle-outline' : 'mdi-circle'"
                      :color="stepId <= (operation.value?.stepId ?? 0) ? '#007acd' : '#778c9b'"
                      size="small"
                    />
                  </template>
                  <template #title>{{ stepId === 110 ? 'Validée' : `Étape ${stepId}` }}</template>
                  <template #subtitle>
                    <div>
                      {{ stepStore.stepsMap[stepId].name }}
                    </div>
                  </template>
                  <template #append>
                    <div class="text-end text-subtitle-2">
                      <template v-if="stepId < (operation.value?.stepId ?? 0)">
                        Validée par :
                        {{ displayFullnameUser(history[stepId]?.user) }}
                        <br />
                        Le :
                        {{ formatHumanReadableLocalDateTime(history[stepId]?.creationDateTime) }}
                      </template>
                    </div>
                  </template>
                </VListItem>
              </VList>
            </VCardText>
          </VCard>
        </template>
      </VDialog>
    </template>
    <template v-if="userStore.isOperationManager" #header-actions>
      <VCardActions
        v-if="
          operation.value && (userHasRole(userStore.currentUser, 'ADMIN_PLUS') || operation.value?.status === 'DOING')
        "
        class="pa-0"
      >
        <div class="d-flex flex-column align-end">
          <div class="d-flex align-center gap-3">
            <DoublonDialog v-if="operation.value.stepId == 0" />
            <NjBtn
              v-if="
                operation.value.stepId === 0 &&
                userHasRole(userStore.currentUser, 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')
              "
              :to="{ name: 'CreateOperationView', params: { simulationId: props.id } }"
              >Nouvelle opération</NjBtn
            >
            <NjBtn
              v-if="operation.value.stepId === 0 && operation.value.businessPlan"
              :to="{ name: 'BusinessPlanOneView', params: { id: operation.value.businessPlan.id } }"
              >Voir Business Plan</NjBtn
            >
          </div>
          <div v-if="operation.value.stepId === 0" class="d-flex justify-end align-center mt-2 gap-2">
            <NjSwitch
              v-model="toProcessValue"
              density="comfortable"
              :loading="savingToProcess"
              label="À traiter"
              @update:model-value="handleToProcessChange"
            />
          </div>
        </div>
        <template v-if="!simulationView && canEditOperation && operation.value.status === 'DOING'">
          <VMenu>
            <template #activator="{ props }">
              <NjIconBtn icon="mdi-dots-horizontal" color="primary" variant="square-outline" v-bind="props" />
            </template>
            <VCard class="d-flex flex-column gap-4 pa-4">
              <NjBtn
                v-if="userCanCancel && operation.value.stepId == 100"
                color="error"
                variant="outlined"
                @click="cancelOperationKOPNCEE"
              >
                Refusé par le PNCEE
              </NjBtn>
              <NjBtn
                v-if="
                  (userCanCancel && operation.value.stepId <= 80 && !operation.value.validateImportInEmmyDateTime) ||
                  userStore.isAdminPlus
                "
                color="error"
                variant="outlined"
                @click="check(improperOperation)"
              >
                Non Conforme
              </NjBtn>
              <NjBtn
                v-if="operation.value.stepId <= 30 && userCanCancel"
                color="error"
                variant="outlined"
                @click="check(giveUpOperation)"
              >
                Abandon
              </NjBtn>
              <NjBtn
                v-if="operation.value.stepId == 40 && userCanCancel"
                color="error"
                variant="outlined"
                @click="check(lostOperation)"
              >
                Offre perdue
              </NjBtn>
            </VCard>
          </VMenu>
          <template
            v-if="operation.value!.operationsGroup && operation.value!.stepId >= 30 && operation.value!.stepId < 50"
          >
            <NjBtn variant="outlined" @click="check(removeFromOperationsGroup)"> Sortir du regroupement </NjBtn>
            <NjBtn
              :to="{
                name: 'OperationsGroupOneView',
                params: { id: operation.value.operationsGroup.id },
              }"
            >
              Gérer le regroupement
            </NjBtn>
          </template>
          <template v-else-if="operation.value!.stepId === 70">
            <template v-if="!operation.value.controlOrderBatch">
              <NjBtn
                variant="outlined"
                :disabled="operation.loading"
                :loading="previousStepLoading"
                @click="goToPreviousStep"
              >
                Etape précédente
              </NjBtn>
              <NjBtn v-if="displayControlOrderButtons" :to="{ name: 'ControlOrderView' }">
                Ajouter à un lot de contrôle
              </NjBtn>
            </template>

            <template v-else>
              <NjBtn
                v-if="
                  !operation.value.controlOrderBatch.step ||
                  operation.value.controlOrderBatch.step === 'CONTROLLED_BY_CONTROL_OFFICE'
                "
                variant="outlined"
                @click="removeOperationFromControlOrderBatch"
              >
                Sortir du lot de contrôle
              </NjBtn>
              <NjBtn
                :to="{
                  name: 'ControlOrderBatchOneView',
                  params: { id: operation.value.controlOrderBatch!.id },
                }"
              >
                Gérer le lot de contrôle
              </NjBtn>
            </template>
            <!-- À enlever quand le workflow de l'arrêté contrôle passera les opérations en étape 80 -->
            <NjBtn
              v-if="operation.value.stepId < 80"
              :disabled="disableValidateStep"
              :loading="operation.loading"
              @click="goToNextStep"
            >
              Valider l'étape {{ operation.value?.stepId }}
            </NjBtn>
          </template>
          <template v-else>
            <NjBtn
              v-if="
                operation.value!.stepId > 20 && (operation.value!.stepId < 80 || operation.value!.emmyFolder == null)
              "
              variant="outlined"
              :disabled="operation.loading"
              :loading="previousStepLoading"
              @click="check(goToPreviousStep)"
            >
              Etape précédente
            </NjBtn>
            <VTooltip
              v-if="operation.value.stepId < 80"
              :disabled="!(isHeadOperationAndAtStep50 || isOperationManualAndAtStep50)"
              location="bottom"
              :text="validateStepTooltipLabel"
            >
              <template #activator="{ props }">
                <div v-bind="props">
                  <NjBtn :disabled="disableValidateStep" :loading="operation.loading" @click="check(goToNextStep)">
                    Valider l'étape {{ operation.value?.stepId }}
                  </NjBtn>
                </div>
              </template>
            </VTooltip>
            <NjBtn
              v-else-if="operation.value.stepId == 80 && !operation.value.emmyFolder"
              @click="
                check(() => {
                  selectEmmyFolderDialog = true
                })
              "
            >
              Ajouter à un dossier EMMY
              <EmmyFolderSelectionDialog
                v-model="selectEmmyFolderDialog"
                :operationfilter-to-add="OperationfilterToAdd"
                title="Préparer le dossier EMMY"
                positive-button="Suivant"
              />
            </NjBtn>

            <template v-else>
              <NjBtn
                v-if="
                  !operation.value.validateImportInEmmyDateTime ||
                  (operation.value.stepId <= 90 && userStore.isAdmin) ||
                  userStore.isAdminPlus
                "
                variant="outlined"
                @click="check(removeOperationFromEmmyFolder)"
              >
                Sortir du dossier EMMY
              </NjBtn>
              <NjBtn
                :to="{
                  name: 'EmmyFolderOneView',
                  params: { id: operation.value.emmyFolder!.id },
                }"
              >
                Gérer le dossier EMMY
              </NjBtn>
            </template>
            <ManageOperationInEmmyFolderDialog
              v-if="manageOperationInEmmyFolderDialog && operation.value.emmyFolder"
              v-model="manageOperationInEmmyFolderDialog"
              :emmy-folder="operation.value.emmyFolder"
            >
              <template #actions>
                <NjBtn
                  @click="
                    router.push({
                      name: 'EmmyFolderOneView',
                      params: { id: operation.value.emmyFolder.id },
                    })
                  "
                >
                  Valider
                </NjBtn>
              </template>
            </ManageOperationInEmmyFolderDialog>
          </template>
        </template>
      </VCardActions>
      <NjBtn
        v-if="
          userStore.hasRole('ADMIN_PLUS') &&
          (operation.value?.stepId ?? 0) > 0 &&
          operation.value?.status !== 'DONE' &&
          operation.value?.status !== 'DOING'
        "
        class="my-4"
        :loading="reactivatingOperation.loading"
        @click="check(reactivateOperation)"
      >
        Réactiver l'opération
      </NjBtn>
      <AlertDialog title="Choix de l'instructeur" v-bind="missingInstructorAlertDialog.props" width="40%">
        <VForm ref="instructorFormRef">
          <RemoteAutoComplete
            v-model="instructor"
            label="Instructeur"
            :query-for-one="(id) => userApi.getOne(id)"
            :query-for-all="
              (s, pageable) =>
                userApi.getAll(pageable, {
                  search: s,
                  roles: ['INSTRUCTEUR'] as ProfileType[],
                  active: true,
                })
            "
            return-object
            :item-title="(item: User) => displayFullnameUser(item)"
            :rules="[requiredRule]"
            infinite-scroll
          />
        </VForm>

        <template #positiveButton>
          <NjBtn @click="onValidateClick"> Valider </NjBtn>
        </template>
      </AlertDialog>
      <CancelOperationDialog
        v-if="!!cancelReason"
        v-model="cancelDialog"
        :cancel-reason="cancelReason"
        :operation="operation.value"
        :cancelling-mail="sendCancellationMail"
        @cancelled="cancelledOperation"
      />
      <VDialog v-if="(operation.value?.stepId ?? 0) < 50" v-model="passedEngagementDate" width="40%">
        <VCard>
          <VCardTitle>Date d'engagement dépassée</VCardTitle>
          <VCardText>
            <VRow class="flex-column">
              <VCol>La date d'engagement est antérieur à la date du jour, voulez-vous la modifier?</VCol>
              <VCol>
                <NjDisplayValue label="Date d'engagement prévisionnelle">
                  <template #value>
                    <NjDatePicker v-model="possibleNewCommitmentDate" />
                  </template>
                </NjDisplayValue>
              </VCol>
            </VRow>
          </VCardText>
          <VCardActions>
            <VSpacer />
            <NjBtn variant="outlined" @click="verifyStandardizedOperationSheetValidity"> Ignorer </NjBtn>
            <NjBtn color="primary" @click="updateEstimatedCommitmentDate"> Enregistrer </NjBtn>
          </VCardActions>
        </VCard>
      </VDialog>
      <ValidStandardizedOperationSheetDialog
        v-if="expiredOperationDialog"
        v-model="expiredOperationDialog"
        v-model:operation="operation.value"
        width="100%"
      />
    </template>
    <template #subtitle>
      <div>
        {{ operation.value?.standardizedOperationSheet.description }}
      </div>
    </template>
    <template #body>
      <DoublonDialog v-if="operation.value?.stepId === 20" v-model="checkDoublon">
        <template #activator><span></span></template>
        <template #complements="args">
          <VForm ref="checksForm">
            <VRow align="center" v-bind="args" class="flex-wrap">
              <VCol cols="3"> Je déclare avoir effectué la recherche dans : </VCol>
              <VCol cols="3" :class="operation.value.eesDuplicate ? '' : 'text-error'">
                <NjSwitch v-model="operation.value.eesDuplicate" label="Base EES*" inline />
              </VCol>
              <VCol cols="3" :class="operation.value.cumul ? '' : 'text-error'">
                <NjSwitch v-model="operation.value.cumul" label="Règles de cumul" inline />
              </VCol>
              <VCol cols="3">
                <NjSwitch v-model="operation.value.customerDuplicate" label="Base du client" inline />
              </VCol>
            </VRow>
          </VForm>
        </template>
        <template #actions>
          <VCardActions>
            <VSpacer />
            <NjBtn variant="outlined" @click="checkDoublon = false"> Annuler </NjBtn>
            <NjBtn
              :disabled="
                !operation.value.eesDuplicate ||
                !operation.value.cumul ||
                (!operation.value.coOwnerShipSyndicateImmatriculationNumber &&
                  !operation.value.coOwnerShipSyndicateName)
              "
              @click="goToNextStep"
            >
              Valider
            </NjBtn>
          </VCardActions>
        </template>
      </DoublonDialog>
      <OperationNextStepDialog
        v-model="nextStepDialog"
        v-model:operation="operation.value"
        @reload-missing-documents="afterSubmitDocumentInNextStepDialog"
        @validated="goToNextStep"
      />
      <DocumentsPreviewDialog
        v-bind="changeStepDialog.props"
        :step-id="operation.value?.stepId ?? 0"
        :operation-id="operation.value?.id ?? 0"
      />
      <VRow class="flex-column w-100" dense>
        <VCol v-if="!simulationView && operation.value && display.lgAndUp" class="flex-grow-0">
          <Stepper
            :id="operation.value.id"
            ref="operationStepperRef"
            :step-id="operation.value.stepId"
            :creation-date-time="operation.value.creationDateTime"
            :creation-user="operation.value.creationUser"
          />
        </VCol>
        <VCol v-if="operation.value && canSendFinalVersion(operation.value)" class="flex-grow-0">
          <OperationFinalVersionToSend :operation="operation.value" @sent="reload" />
        </VCol>
        <template
          v-if="
            operation.value &&
            (operation.value.stepId === 60 || operation.value.stepId === 70) &&
            operation.value.controlReportIssueDate &&
            operation.value.controlReportIssueDate >= MIN_WORKFLOW_CONTROL_REPORT_LOCALDATE
          "
        >
          <VCol v-if="controlReportInfos!.differenceFromNowInDays >= 0" class="flex-grow-0">
            <VAlert type="warning">
              Le rapport de contrôle doit être envoyé au bénéficiaire.<br />
              Date limite d'envoi : {{ formatHumanReadableLocalDate(controlReportInfos?.limitDate) }} (passée cette
              date, l’opération est Non Conforme)
            </VAlert>
          </VCol>
          <VCol v-else class="flex-grow-0">
            <VAlert type="error">
              Vous avez dépassé la date limite d'envoi du rapport de controle
              {{ formatHumanReadableLocalDate(controlReportInfos?.limitDate) }}. <br />Veuillez passer l'opération en
              "Non Conforme".
            </VAlert>
          </VCol>
        </template>
        <template
          v-if="
            operation.value &&
            operation.value.stepId >= 80 &&
            operation.value.controlReportIssueDate &&
            operation.value.controlReportIssueDate >= MIN_WORKFLOW_CONTROL_REPORT_LOCALDATE &&
            operation.value.documentTypeIdsSent?.includes(CONTROL_REPORT_DOCUMENT_TYPE_ID) !== true
          "
        >
          <VCol v-if="controlReportInfos!.differenceFromNowInDays >= 0" class="flex-grow-0">
            <VAlert type="warning">
              Vous avez un rapport de contrôle à envoyer.<br />
              Date limite d'envoi : {{ formatHumanReadableLocalDate(controlReportInfos?.limitDate) }} (passée cette date
              l’opération est Non Conforme)<br />
              <VLink v-if="userStore.hasRole('AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')" @click="sendControlReport"
                >Envoyer le rapport de contrôle</VLink
              >
            </VAlert>
          </VCol>
          <VCol v-else class="flex-grow-0">
            <VAlert type="error">
              Vous avez dépassé la date limite d'envoi du rapport de controle
              {{ formatHumanReadableLocalDate(controlReportInfos?.limitDate) }}. <br />Veuillez passer l'opération en
              "Non Conforme".
            </VAlert>
          </VCol>
        </template>
        <ControlOrderErrorAlert
          v-if="operation.value && operationStepperRef?.operationStepHistoryList.value"
          class="flex-grow-0"
          :operation="operation.value"
          :operation-step-histories="operationStepperRef?.operationStepHistoryList.value"
          :can-validate-step70="canValidateStep70"
        />
        <VCol
          v-if="
            operation.value?.stepId == 50 &&
            operation.value.actualEndWorksDate &&
            operation.value.standardizedOperationSheet.controlOrderNature == 'HUNDRED_PERCENT'
          "
          class="flex-grow-0"
        >
          <ErrorAlert
            :type="daysRemainingToValidateStep50 >= 0 ? 'info' : 'error'"
            :message="
              daysRemainingToValidateStep50 >= 0
                ? `Il reste ${daysRemainingToValidateStep50} jours pour valider l'étape 50`
                : `La validité de l'étape 50 est expirée depuis ${Math.abs(daysRemainingToValidateStep50)} jours`
            "
          />
        </VCol>
        <CommentaireDialog
          ref="commentaireDialogRef"
          v-model="commentaireDialog.props.modelValue"
          v-model:mandatory-message="mandatoryMessage"
          v-model:message="defaultMessage"
          :operation="operation.value"
          :no-link="!!mandatoryMessage"
          :advised-recipient="advisedRecipient"
          :mandatory-concerned-document-type-ids
          :daf-mode
          @send="handleSentMessage"
          @close="commentaireDialog.props['onClick:negative']"
        />
        <CommentaireDialog
          v-model="toProcessCommentaireDialog.props.modelValue"
          v-model:mandatory-message="toProcessMandatoryMessage"
          :operation="operation.value"
          :no-link="!!toProcessMandatoryMessage"
          :advised-recipient="toProcessAdvisedRecipient"
          :meta-message-request="toProcessMetaMessageRequest"
          @send="toProcessCommentaireDialog.props['onClick:positive']"
          @update:model-value="
            (modelvalue) => {
              if (!modelvalue) toProcessCommentaireDialog.props['onClick:negative']?.()
            }
          "
        />
        <VCol class="d-flex overflow-hidden">
          <VRow class="w-100" dense>
            <VCol v-show="!expandedDetail" cols="8">
              <VCard class="content-layout">
                <VCardText class="content-layout__main pa-0 overflow-hidden">
                  <VRow class="flex-column content-layout" no-gutters>
                    <VCol class="content-layout__header">
                      <VTabs v-model="tab">
                        <VTab>Documents</VTab>
                        <VTab>Traçabilité</VTab>
                      </VTabs>
                      <VDivider />
                    </VCol>
                    <VCol class="content-layout__main">
                      <VWindow v-model="tab" style="height: calc(100% - 12px)">
                        <VWindowItem class="h-100">
                          <div class="content-layout">
                            <OperationDocumentAllView
                              v-if="operation.value"
                              ref="operationDocumentAllViewRef"
                              :operation="operation.value"
                              @send="(id) => send(id)"
                              @save-document="onSaveOperationDocument"
                            />
                          </div>
                        </VWindowItem>
                        <VWindowItem class="h-100">
                          <div class="content-layout">
                            <div v-if="operation.value" class="flex-column content-layout content-layout__main">
                              <OperationEventHistoryView
                                ref="operationEventHistoryRef"
                                :operation="operation.value"
                                @treated="handleAtypicalValuation($event.accepted)"
                              />
                              <VDivider />
                              <div class="content-layout__footer d-flex justify-end ma-2">
                                <!-- <VSpacer /> -->
                                <!-- <VCol class="flex-grow-0"> -->
                                <NjBtn @click="commentaireDialog.confirm()">Commentaire</NjBtn>
                                <!-- </VCol> -->
                              </div>
                            </div>
                          </div>
                        </VWindowItem>
                      </VWindow>
                    </VCol>
                  </VRow>
                </VCardText>
              </VCard>
            </VCol>
            <VCol :cols="expandedDetail ? 12 : 4" style="flex-basis: auto">
              <OperationCard
                v-if="!operation.loading"
                ref="cardRef"
                v-model="expandedDetail"
                :operation="operation.value!"
                mode="detail"
                @update:operation="(ope: Operation) => (operation.value = ope)"
                @saved="reload"
                @force-reload="reload"
                @request-reload="reload"
              />
              <VProgressCircular v-else color="primary" class="ms-4" indeterminate />
            </VCol>
          </VRow>
        </VCol>
      </VRow>
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import { emmyFolderApi } from '@/api/emmyFolder'
import { userApi } from '@/api/user'
import { simulationApi } from '@/api/simulation'
import NjBtn from '@/components/NjBtn.vue'
import NjPage from '@/components/NjPage.vue'
import NjSwitch from '@/components/NjSwitch.vue'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { type DialogStoreRequest, useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import {
  type CeeStockEntry,
  mapCeeStockEntryToCeeStockEntryComparable,
  mapOperationToCeeStockEntryComparable,
  shouldCreateCeeStockEntry,
} from '@/types/ceeStockEntry'
import type { ControlOrderBatch } from '@/types/controlOrder'
import { parseLocalDate } from '@/types/date'
import type { DocumentType } from '@/types/documentType'
import { dafMail } from '@/types/message'
import { isStep70Required, mapToOperationRequest, type Operation, type OperationStatus } from '@/types/operation'
import type { Page } from '@/types/pagination'
import { requiredRule } from '@/types/rule'
import { displayFullnameUser, type ProfileType, type User, userHasRole } from '@/types/user'
import { add, differenceInBusinessDays, differenceInDays } from 'date-fns'
import { VCol, VRow, VSpacer, VTooltip } from 'vuetify/components'
import { VForm } from 'vuetify/components/VForm'
import EmmyFolderSelectionDialog from '../components/EmmyFolderSelectionDialog.vue'
import DoublonDialog from './DoublonDialog.vue'
import ManageOperationInEmmyFolderDialog from './emmyfolder/ManageOperationInEmmyFolderDialog.vue'
import CancelOperationDialog from './operation/dialog/CancelOperationDialog.vue'
import CommentaireDialog from './operation/dialog/CommentaireDialog.vue'
import DocumentsPreviewDialog from './operation/dialog/DocumentsPreviewDialog.vue'
import OperationNextStepDialog from './operation/dialog/OperationNextStepDialog.vue'
import OperationCard from './operation/OperationCard.vue'
import Stepper from './operation/Stepper.vue'
import OperationDocumentAllView from './operationdocument/OperationDocumentAllView.vue'
import OperationEventHistoryView from './operationEventHistory/OperationEventHistoryView.vue'
import ValidStandardizedOperationSheetDialog from './ValidStandardizedOperationSheetDialog.vue'

import { operationApi, type OperationFilter } from '@/api/operation'
import { useStepsStore } from '@/stores/steps'
import { formatHumanReadableLocalDateTime } from '@/types/date'
import { canSendFinalVersion } from '@/types/operation'
import { parseInt } from 'lodash'
import { useDisplay } from 'vuetify'
import { VProgressCircular } from 'vuetify/components/VProgressCircular'
import { missingDocumentTypeIdsKey } from './dashboard/keys'
import {
  deleteControlOrderBatchDialogRequest,
  removeOperationFromControlOrderBatchRequest,
} from './dialog/dialogRequest'
import type SendDocumentResult from './document/sendDocumentResult'
import ControlOrderErrorAlert from './operation/ControlOrderErrorAlert.vue'
import OperationFinalVersionToSend from './operation/OperationFinalVersionToSend.vue'
import OperationStepChip from './operation/OperationStepChip.vue'
import { formatHumanReadableLocalDate } from '@/types/date'
import { MIN_WORKFLOW_CONTROL_REPORT_LOCALDATE } from '@/types/controlOrder'
import { CONTROL_REPORT_DOCUMENT_TYPE_ID } from '@/types/documentType'
import { toProcessMail, type MetaMessageRequest } from '@/types/message'
import { useConfirmAlertDialog } from '@/types/alertDialog'

const props = defineProps({
  id: {
    type: Number,
    required: true,
  },
})

const displayControlOrderButtons = computed(() => import.meta.env.VITE_FEATURE_CONTROL_ORDER === 'true')

const snackbarStore = useSnackbarStore()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const stepStore = useStepsStore()
const display = useDisplay()

const canGoBack = () => {
  router.back()
}

const expandedDetail = ref(false)

const errorMessage = ref()
const operation = ref(emptyValue<Operation>())
const anyCurrentRequest = ref(emptyValue<any>())
const cardRef = ref<typeof OperationCard | null>(null)

const commentaireDialog = useConfirmAlertDialog()
const reloadMessages = ref(false)
const advisedRecipient = ref<string[]>([])
const mandatoryConcernedDocumentTypeIds = ref<number[]>()

const toProcessValue = ref(false)
const savingToProcess = ref(false)
const toProcessCommentaireDialog = useConfirmAlertDialog()
const toProcessMandatoryMessage = ref('')
const toProcessAdvisedRecipient = ref<string[]>([])
const toProcessMetaMessageRequest = ref<MetaMessageRequest>()

watch(
  () => operation.value?.value?.toProcess,
  (newValue) => {
    toProcessValue.value = newValue ?? false
  },
  { immediate: true }
)

watch(
  () => cardRef.value?.operationFormRef?.mutableOperation?.toProcess,
  (newValue) => {
    if (cardRef.value?.localEdit && newValue !== undefined) {
      toProcessValue.value = newValue
    }
  },
  { immediate: true }
)

const sendMailForProcessOperations = async (operationToProcess: Operation): Promise<boolean> => {
  try {
    toProcessMetaMessageRequest.value = { '@type': 'OperationToProcessMessageRequest' }
    toProcessMandatoryMessage.value = toProcessMail(operationToProcess)
    toProcessAdvisedRecipient.value = []

    const usersResponse = await userApi.getAll(
      { size: 1000 },
      { roles: ['AGENCE_PLUS'], entityIds: [operationToProcess.entity.id], active: true }
    )

    toProcessAdvisedRecipient.value = usersResponse.data.content
      .map((user) => user.email)
      .filter((email) => email) as string[]
  } catch {
    snackbarStore.setError("Nous n'avons pas pu récupérer la liste des contacts pour le traitement de la simulation")
  }

  const result = await toProcessCommentaireDialog.confirm()

  toProcessMandatoryMessage.value = ''
  toProcessAdvisedRecipient.value = []
  toProcessMetaMessageRequest.value = { '@type': 'OperationToProcessMessageRequest' }

  return result
}

const handleToProcessChange = async (newValue: boolean) => {
  if (!operation.value?.value) return

  if (cardRef.value?.localEdit) {
    if (cardRef.value?.operationFormRef?.mutableOperation) {
      cardRef.value.operationFormRef.mutableOperation.toProcess = newValue
      toProcessValue.value = newValue
      if (newValue) {
        snackbarStore.setSuccess('Simulation indiquée comme "à traiter"')
      }
    }
    return
  }

  const originalValue = operation.value.value.toProcess

  try {
    savingToProcess.value = true

    if (newValue && !originalValue) {
      const mailSent = await sendMailForProcessOperations(operation.value.value)
      if (mailSent === undefined || mailSent === false) {
        toProcessValue.value = originalValue
        savingToProcess.value = false
        return
      }
    }

    const updatedOperation = { ...operation.value.value, toProcess: newValue }
    const operationRequest = mapToOperationRequest(updatedOperation)
    await simulationApi.updateSimulation(operation.value.value.id, operationRequest)

    operation.value.value = updatedOperation
    load()
    snackbarStore.setSuccess('Statut "À traiter" mis à jour avec succès')
  } catch {
    toProcessValue.value = originalValue
    snackbarStore.setError('Erreur lors de la sauvegarde du statut "À traiter"')
  } finally {
    savingToProcess.value = false
  }
}

const checkDoublon = ref(false)
const cancelDialog = ref(false)
const nextStepDialog = ref(false)
const changeStepDialog = useConfirmAlertDialog()

const possibleNewCommitmentDate = ref('')

const passedEngagementDate = ref(false)
const expiredOperationDialog = ref(false)
const { history, steps } = useOperationHistory(
  computed(() => ({
    id: operation.value.value?.id ?? 0,
    stepId: operation.value.value?.stepId ?? 0,
    creationUser: operation.value.value?.creationUser,
    creationDateTime: operation.value.value?.creationDateTime,
  }))
)

const load = async () => {
  await handleAxiosPromise(operation, simulationApi.findById(props.id))
  expiredOperationDialog.value = !isStandardizedOperationSheetAndCommitmentCompatible(operation.value.value!)
  possibleNewCommitmentDate.value = operation.value.value!.estimatedCommitmentDate
  if (route.query?.duplicate === 'true') {
    expandedDetail.value = true
  }

  if (route.name === 'OperationOneView' && operation.value.value?.stepId === 0) {
    router.push({ name: 'SimulationOneView', params: route.params })
  }
}
const reload = () => {
  operationEventHistoryRef.value?.reload()
  handleAxiosPromise(operation, simulationApi.findById(props.id))
}

watch(
  () => props.id,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

const cancelReason = ref<OperationStatus | null>(null)

const improperOperation = () => {
  cancelReason.value = 'IMPROPER'
  cancelDialog.value = true
}

const giveUpOperation = () => {
  cancelReason.value = 'CANCELLED'
  cancelDialog.value = true
}

const lostOperation = () => {
  cancelReason.value = 'LOST'
  cancelDialog.value = true
}

const cancelOperationKOPNCEE = () => {
  cancelReason.value = 'KO_PNCEE'
  cancelDialog.value = true
}

const userCanCancel = computed(() =>
  userHasRole(userStore.currentUser, 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'TERRITOIRE', 'ADMIN', 'ADMIN_PLUS')
)
const canEditOperation = computed(() =>
  operation.value.value!.stepId <= 50
    ? userHasRole(userStore.currentUser, 'AGENCE', 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')
    : userHasRole(userStore.currentUser, 'SIEGE', 'INSTRUCTEUR', 'ADMIN_PLUS')
)

const OperationfilterToAdd = computed<OperationFilter>(() => {
  const id = operation.value?.value?.id
  return {
    operationIds: id !== undefined ? [id] : [],
  }
})

const sendDocument = async (
  documentTypeId: number,
  dialogStoreRequest: DialogStoreRequest,
  noDocumentErrorMessage: string
) => {
  if (
    operation.value.value?.documentTypeIdsSent?.includes(documentTypeId) &&
    !(await dialogStore.addAlert(dialogStoreRequest))
  ) {
    return
  }
  const documents = await operationDocumentApi
    .findAll(
      {
        operationId: props.id,
        documentTypeId: documentTypeId,
        active: true,
      },
      {
        size: 0,
      }
    )
    .catch((e) => {
      handleAxiosException(e)
      return { data: makeEmptyPage() }
    })

  if (documents.data.totalElements === 0) {
    snackbarStore.setError(noDocumentErrorMessage)
    return
  }
  mandatoryMessage.value = ''
  if (documentTypeId === CONTROL_REPORT_DOCUMENT_TYPE_ID) {
    advisedRecipient.value = [operation.value.value?.beneficiary?.email ?? ''].filter((it) => it)
  } else {
    advisedRecipient.value = operation.value.value?.creationUser.email
      ? [operation.value.value?.creationUser.email]
      : []
  }
  mandatoryConcernedDocumentTypeIds.value = [documentTypeId]

  switch (documentTypeId) {
    case CONTROL_REPORT_DOCUMENT_TYPE_ID: {
      defaultMessage.value = `Madame, Monsieur,\n\nDans le cadre de la réglementation encadrant le dispositif des Certificats d'Économies d'Énergie (CEE), nous vous transmettons ci-joint le rapport de contrôle sur site relatif à votre opération ${operation.value.value?.operationName}, à l'adresse ${formatAddressable(operation.value.value?.finalAddress)}.\n\nCe document constitue une pièce justificative pouvant être exigée lors d’un éventuel contrôle mené par les pouvoirs publics.\n\nNous vous recommandons de le conserver précieusement.\n\nNous vous souhaitons bonne réception et restons à votre disposition pour tout complément d’information.`
      break
    }
    default: {
      defaultMessage.value = ''
    }
  }

  if (!(await commentaireDialog.confirm())) {
    cancelDialog.value = false
    return false
  }

  mandatoryMessage.value = ''
  advisedRecipient.value = []
  mandatoryConcernedDocumentTypeIds.value = undefined

  await handleAxiosPromise(
    anyCurrentRequest,
    operationApi.updateSentDocumentTypeIds(props.id, documentTypeId).catch(async (e) => {
      snackbarStore.setError(await handleAxiosException(e))
      throw e
    })
  )

  // TODO ne recharger que les documents et historiques
  reload()

  return true
}

const sendConvention = async () => {
  return await sendDocument(
    parseInt(adminConfigurationStore.conventionDocumentTypeId!.data),
    {
      maxWidth: '640px',
      title: 'Convention déjà envoyé',
      message: 'La convention a déjà été envoyé au créateur de la simulation.\nVoulez-vous la renvoyer ?',
    },
    "Vous devez d'abord fournir la convention dans Capte avant de pouvoir l'envoyer"
  )
}

const sendSwornStatement = async () => {
  return await sendDocument(
    parseInt(adminConfigurationStore.swornStatementDocumentTypeId!.data),
    {
      maxWidth: '640px',
      title: "Attestation sur l'honneur déjà envoyé",
      message: "L'attestation sur l'honneur a déjà été envoyé au créateur de la simulation.\nVoulez-vous la renvoyer ?",
    },
    "Vous devez d'abord fournir l'attestation sur l'honneur dans Capte avant de pouvoir l'envoyer"
  )
}

const sendPvReception = async () => {
  return await sendDocument(
    parseInt(adminConfigurationStore.pvDeReceptionDocumentTypeId!.data),
    {
      maxWidth: '640px',
      title: 'Le PV de réception a déjà été envoyé',
      message: 'Le PV de réception a déjà été envoyé au créateur de la simulation.\nVoulez-vous la renvoyer ?',
    },
    "Vous devez d'abord fournir le PV de réception dans Capte avant de pouvoir l'envoyer"
  )
}

const sendControlReport = async () => {
  return await sendDocument(
    CONTROL_REPORT_DOCUMENT_TYPE_ID,
    {
      maxWidth: '640px',
      title: 'Rapport de contrôle déjà envoyé',
      message: 'Le rapport de contrôle a déjà été envoyé au client.\nVoulez-vous le renvoyer ?',
    },
    "Vous devez d'abord fournir le rapport de contrôle dans Capte avant de pouvoir l'envoyer"
  )
}

const send = (documentTypeId: number) => {
  switch (documentTypeId) {
    case adminConfigurationStore.conventionDocumentTypeId?.valueAsInt:
      return sendConvention()
    case adminConfigurationStore.swornStatementDocumentTypeId?.valueAsInt:
      return sendSwornStatement()
    case adminConfigurationStore.pvDeReceptionDocumentTypeId?.valueAsInt:
      return sendPvReception()
    case adminConfigurationStore.vfDocumentTypeId?.valueAsInt:
      return sendVF()
    case CONTROL_REPORT_DOCUMENT_TYPE_ID:
      return sendControlReport()
  }
}

const sendCancellationMail = async () => {
  const mailOperation = {
    ...operation.value.value!,
    classicCumac: 0,
    precariousnessCumac: 0,
    status: cancelReason.value!,
  }

  mandatoryMessage.value = dafMail(mailOperation, false)
  advisedRecipient.value = mailOperation!.entity.entityDetails.effectiveDafMail
  defaultMessage.value = ''

  if (!(await commentaireDialog.confirm())) {
    cancelDialog.value = false
    return false
  }
  mandatoryMessage.value = ''
  advisedRecipient.value = []
  return true
}

const reactivatingOperation = ref(emptyValue<Operation>())
const instructor = ref<User>()
const missingInstructorAlertDialog = useConfirmAlertDialog()
const instructorFormRef = ref<VForm | null>(null)
const onValidateClick = async () => {
  if ((await instructorFormRef.value!.validate()).valid) {
    missingInstructorAlertDialog.props['onClick:positive']()
  }
  return
}
const reactivateOperation = async () => {
  if (!operation.value.value?.instructor) {
    if (!(await missingInstructorAlertDialog.confirm())) {
      return
    }
  }
  handleAxiosPromise(reactivatingOperation, operationApi.updateStatus(operation.value.value!.id, 'DOING', undefined), {
    afterSuccess: async () => {
      if (instructor.value) {
        await operationApi.updateInstructor(operation.value.value!.id, instructor.value!.id)
      }
      snackbarStore.setSuccess('Opération réactivée')
      load()
    },
    afterError: () => snackbarStore.setError("Une erreur est survenue lors de la réactivation de l'opération"),
  })
}

const mandatoryMessage = ref('')
const defaultMessage = ref('')
const dafMode = ref(false)

const goToNextStep = async () => {
  const operationToSend = operation.value.value
  const operationRequest = mapToOperationRequest(operationToSend!)

  if (cardRef.value?.differentValuation) {
    cardRef.value!.updatingValuation = true
    return
  }

  if (operationToSend?.stepId === 20 && (!operationToSend.eesDuplicate || !operationToSend.cumul)) {
    checkDoublon.value = true
    return
  }
  checkDoublon.value = false

  if (
    ((operationRequest.stepId >= 30 && operationRequest.stepId <= 70) || isDocumentMissing()) &&
    !nextStepDialog.value
  ) {
    nextStepDialog.value = true
    return
  }

  let openCommentaireDialog = false
  let lastCeeStockEntry: CeeStockEntry | undefined = undefined
  if (
    operationRequest.stepId == 50 &&
    operation.value.value?.standardizedOperationSheet.operationCode !== 'SPÉCIFIQUE' &&
    operation.value.value?.backToStep50Counter == 0
  ) {
    openCommentaireDialog = true
  } else if (operationRequest.stepId >= 50) {
    try {
      const res = await ceeStockEntryApi.findAll(
        props.id,
        {},
        {
          active: true,
        }
      )
      lastCeeStockEntry = res.data.content[0]
      openCommentaireDialog =
        res.data.content.length > 0 &&
        shouldCreateCeeStockEntry(
          mapCeeStockEntryToCeeStockEntryComparable(lastCeeStockEntry),
          mapOperationToCeeStockEntryComparable(operation.value.value!)
        )
    } catch (e) {
      snackbarStore.setError(await handleAxiosException(e))
      return
    }
  }

  if (openCommentaireDialog) {
    mandatoryMessage.value = dafMail(
      operation.value.value!,
      !operation.value.value!.backToStep50Counter && !(operationRequest.stepId > 50)
    )
    advisedRecipient.value = operation.value.value!.entity.entityDetails.effectiveDafMail
    defaultMessage.value = ''

    dafMode.value = true
    if (!(await commentaireDialog.confirm())) {
      return
    }
    mandatoryMessage.value = ''
    advisedRecipient.value = []
  }

  dafMode.value = false

  if (operationRequest.stepId === 10 && operationRequest.eesDuplicate && operationRequest.cumul) {
    operationRequest.stepId = 30
  } else if (operationRequest.stepId === 60 && !isStep70Required(operation.value.value!)) {
    operationRequest.stepId = 80
  } else {
    operationRequest.stepId += 10
  }

  await handleAxiosPromise(operation, simulationApi.updateSimulation(operationToSend!.id, operationRequest), {
    afterSuccess: () => {
      snackbarStore.setSuccess("Passage d'étape effectué avec succès")
      loadMissingDocuments()
      nextStepDialog.value = false
      changeStepDialog.confirm().finally(() => {
        commitmentOrStandardizedOperationSheetExpired()
        operationEventHistoryRef.value?.reload()
      })
      if (
        operation.value.value!.stepId === 60 &&
        (operation.value.value!.controlReportIssueDate ?? '1970-01-01') >= '2025-06-01' // Workflow du rapport de controle ne s'applique que pour les rapports émis depuis le 01/06/2025
      ) {
        const limitDate = addBusinessDay(operation.value.value!.controlReportIssueDate!, CONTROL_REPORT_MAX_RETURN_DAYS)
        const durationInDays = differenceInBusinessDays(limitDate, new Date())
        if (durationInDays < 0) {
        } else {
          dialogStore.addAlert2({
            title: 'Rapport de contrôle',
            message: `Le pôle production vérifie les livrables de contrôle. Vous recevrez prochainement un mail pour l’envoi du rapport au client.\nDate limite d'envoi : ${formatHumanReadableLocalDate(limitDate)} (passée cette date, l’opération est Non Conforme)`,
            maxWidth: '640px',
          })
        }
      }
    },
    afterError: (err) => {
      snackbarStore.setError(err)
    },
  })

  // TODO
  if (operation.value.value?.controlReportIssueDate && operationRequest.stepId === 80) {
    defaultMessage.value =
      "Vous pouvez envoyer le rapport de contrôle.\nDate limite d'envoi : " +
      formatHumanReadableLocalDate(controlReportInfos.value!.limitDate) +
      ' (soit dans ' +
      (controlReportInfos.value!.differenceFromNowInDays + ' jour(s) ouvré(s))')
    advisedRecipient.value = operation.value.value!.entity.entityDetails.effectiveDafMail

    advisedRecipient.value = (
      await userApi
        .getAll(
          { size: 1000 },
          { active: true, roles: ['AGENCE_PLUS'], entityIds: [operation.value.value!.leadingEntity.id] }
        )
        .catch((e) => {
          snackbarStore.setError(
            "Nous n'avons pas pu récupérer la liste des contacts pour le traitement de la simulation"
          )
          throw e
        })
    ).data.content
      .map((it) => it.email)
      .filter((it) => it)
      .sort() as string[]

    dafMode.value = false
    if (!(await commentaireDialog.confirm())) {
      return
    }
    mandatoryMessage.value = ''
    advisedRecipient.value = []
    defaultMessage.value = ''
  }
}

const previousStepLoading = ref(false)
const goToPreviousStep = async () => {
  const operationRequest = mapToOperationRequest(operation.value.value!)
  if (operationRequest.stepId === 60) {
    previousStepLoading.value = true
    advisedRecipient.value = (
      await userApi
        .getAll(
          { size: 1000 },
          { active: true, roles: ['AGENCE_PLUS'], entityIds: [operation.value.value!.leadingEntity.id] }
        )
        .catch((e) => {
          snackbarStore.setError(
            "Nous n'avons pas pu récupérer la liste des contacts pour le traitement de la simulation"
          )
          throw e
        })
    ).data.content
      .map((it) => it.email)
      .filter((it) => it)
      .sort() as string[]

    await commentaireDialog.confirm().finally(() => {
      previousStepLoading.value = false
    })
  }
  if (operationRequest.stepId === 80 && !isStep70Required(operation.value.value!)) {
    operationRequest.stepId = 60
  } else {
    operationRequest.stepId -= 10
  }
  handleAxiosPromise(operation, simulationApi.updateSimulation(operation.value.value!.id, operationRequest), {
    afterSuccess: () => {
      operationEventHistoryRef.value?.reload()
      loadMissingDocuments()
    },
  })
}

const handleSentMessage = (type?: 'finalVersion' | 'askingAtypical') => {
  reloadMessages.value = true
  operationEventHistoryRef.value?.reload()
  commentaireDialog.props['onClick:positive']()

  if (type === 'finalVersion') {
    reload()
  }
}

const handleAtypicalValuation = (accepted: boolean) => {
  if (accepted) {
    load()
  }
  operationEventHistoryRef.value?.reload()
}

const commitmentOrStandardizedOperationSheetExpired = () => {
  if (operation.value.value!.stepId < 50 && !operation.value.value?.signedDate) {
    passedEngagementDate.value = new Date() > new Date(operation.value.value!.estimatedCommitmentDate)
    return
  }
  expiredOperationDialog.value = !isStandardizedOperationSheetAndCommitmentCompatible(operation.value.value!)
}

const updateEstimatedCommitmentDate = () => {
  const request = mapToOperationRequest({
    ...operation.value.value!,
    estimatedCommitmentDate: possibleNewCommitmentDate.value,
  })
  handleAxiosPromise(operation, simulationApi.updateSimulation(operation.value.value!.id, request), {
    afterSuccess: () => {
      snackbarStore.setSuccess("La date d'engagement provisoire a bien été modifiée")
      verifyStandardizedOperationSheetValidity()
    },
    afterError: () =>
      snackbarStore.setError(
        operation.value.error ?? "Une erreur est survenue lors de la modification de la date d'engagement provisoire"
      ),
  })
}

const verifyStandardizedOperationSheetValidity = () => {
  passedEngagementDate.value = false
  expiredOperationDialog.value = !isStandardizedOperationSheetAndCommitmentCompatible(operation.value.value!)
}

watch(
  () => commentaireDialog.props.modelValue,
  (v) => {
    if (!v) {
      advisedRecipient.value = []
      mandatoryMessage.value = ''
    }
  }
)

// Update du expended si on passe les premières étapes ou non
watch(
  () => operation.value.value,
  () => {
    if (!missingDocumentTypes.value.value) {
      loadMissingDocuments()
    }
  }
)

const isDocumentMissing = () => missingDocumentTypes.value.value && missingDocumentTypes.value.value?.totalElements > 0

//Gestion du VTab
const tab = ref()

const missingDocumentTypes = ref(emptyValue<Page<DocumentType>>())
const loadMissingDocuments = () => {
  return handleAxiosPromise(
    missingDocumentTypes,
    documentTypeApi.getAll({ size: 100 }, { missingToValidateOperation: props.id })
  ).then((it) => it.data.content)
}
provide(missingDocumentTypeIdsKey, {
  missingDocumentTypes: computed(() => missingDocumentTypes.value.value?.content ?? []),
  reload: loadMissingDocuments,
})

watch(nextStepDialog, (v) => {
  if (v) {
    loadMissingDocuments()
  }
})

const isOperationManualAndAtStep50 = computed(
  () =>
    !userStore.isSiege &&
    operation.value.value!.stepId == 50 &&
    (operation.value.value?.parameterValues ?? []).length == 0
)

const isHeadOperationAndAtStep50 = computed(
  () => operation.value.value?.stepId == 50 && operation.value.value.headOperation
)

const validateStepTooltipLabel = computed(() => {
  if (isHeadOperationAndAtStep50.value) {
    return "Une opération chapeau ne peut pas valider l'étape 50"
  } else {
    return "Les paramètres de calculs doivent être complétés avant l'envoi en instruction"
  }
})

const currentAnnualNotSatisfyingRate = ref<number>(0)

watch(
  () => operation.value.value,
  (v) => {
    if (v?.controlOrderBatch?.batchCode) {
      annualNotSatisfyingRateApi
        .findByYear(parseInt(v.controlOrderBatch.creationDateTime.split('-')[0] ?? 0))
        .then((res) => {
          currentAnnualNotSatisfyingRate.value = res.data.notSatisfyingRate
        })
        .catch(async (err) => {
          snackbarStore.setError(await handleAxiosException(err))
        })
    }
  }
)

const validateSiteRate = (controlOrderBatch: ControlOrderBatch | null | undefined) => {
  return (
    (controlOrderBatch?.usedMinimumSatisfyingControlRate?.siteRate ?? 100) <=
    (controlOrderBatch?.siteComplianceRate ?? 0)
  )
}

const validateContactRate = (controlOrderBatch: ControlOrderBatch | null | undefined) => {
  return (
    (controlOrderBatch?.usedMinimumSatisfyingControlRate?.contactRate ?? 100) <=
    (controlOrderBatch?.contactComplianceRate ?? 0)
  )
}

const validateMinimumByFosRate = computed(() => {
  const controlOrderType = operation.value.value?.standardizedOperationSheet.controlOrderType
  const controlOrderBatch = operation.value.value?.controlOrderBatch

  if (controlOrderType == 'SITE') {
    return validateSiteRate(controlOrderBatch)
  } else if (controlOrderType == 'CONTACT') {
    return validateContactRate(controlOrderBatch)
  } else if (controlOrderType == 'SITE_AND_CONTACT' && controlOrderBatch?.contactComplianceRate) {
    return validateSiteRate(controlOrderBatch) && validateContactRate(controlOrderBatch)
  } else if (controlOrderType == 'SITE_AND_CONTACT') {
    return (
      (controlOrderBatch?.usedMinimumSatisfyingControlRate?.siteRate ?? 100) +
        (controlOrderBatch?.usedMinimumSatisfyingControlRate?.contactRate ?? 100) <=
      (controlOrderBatch?.siteComplianceRate ?? 0)
    )
  } else {
    return false
  }
})

const validateRate = computed(() => {
  const controlOrderBatch = operation.value.value?.controlOrderBatch

  return (
    (controlOrderBatch?.notSatisfyingRate ?? 100) <= currentAnnualNotSatisfyingRate.value &&
    validateMinimumByFosRate.value
  )
})

const canValidateStep70 = computed(
  () =>
    operation.value.value?.stepId == 70 &&
    (import.meta.env.VITE_FEATURE_CONTROL_ORDER === 'false' ||
      ['SATISFYING', 'NOT_VERIFIABLE'].includes(operation.value.value?.controlOrderDetails?.controlOrderStatus ?? '') ||
      operation.value.value.controlOrderDetails?.afterSalesServiceStatus ==
        'ADMINISTRATIVE_CONTROL_OF_AFTER_SALES_SERVICE_SHEETS_AND_SUPPORT_DOCUMENTS' ||
      (!operation.value.value.controlOrderDetails?.controlOrderType && validateRate.value))
)

//disabled valider l'étape
const disableValidateStep = computed(
  () =>
    (operation.value.value &&
      !!(
        operation.value.value.operationsGroup &&
        (operation.value.value.stepId == 30 || operation.value.value.stepId == 40)
      )) ||
    isHeadOperationAndAtStep50.value ||
    isOperationManualAndAtStep50.value ||
    (operation.value.value?.stepId == 70 && !!operation.value.value?.controlOrderBatch && !canValidateStep70.value)
)

const operationDocumentAllViewRef = ref<typeof OperationDocumentAllView | null>(null)
const afterSubmitDocumentInNextStepDialog = () => {
  loadMissingDocuments()
  operationDocumentAllViewRef.value?.reload()
  operationEventHistoryRef.value?.reload()
}

const simulationView = computed(() => router.currentRoute.value.name == 'SimulationOneView')

const removeFromOperationsGroup = async () => {
  if (
    await dialogStore.addAlert({
      title: "Retirer l'opération",
      message:
        "Attention! En sortant cette opération du regroupement, tous les documents présents au niveau de regroupement seront par conséquent retirés de l'opération.\n" +
        (operation.value.value!.operationsGroup?.atypicalClassicValuationValue != null ||
        operation.value.value!.operationsGroup?.atypicalPrecariousnessValuationValue != null
          ? "De plus, en sortant l'opération du regroumement, sa valorisation atypique (issue du regroupement) sera annulée. Une nouvelle demande devra alors être faite.\n"
          : '') +
        'Voulez-vous continuer?',
      maxWidth: '640px',
    })
  ) {
    await operationsGroupApi
      .removeOperation(operation.value.value!.id)
      .then(() => {
        snackbarStore.setSuccess("L'opération a bien été sortie du regroupement")
        handleAxiosPromise(operation, simulationApi.findById(props.id))
      })
      .catch(async (err) =>
        snackbarStore.setError((await handleAxiosException(err)) ?? 'Erreur lors de la sortie du regroupement')
      )
    operationEventHistoryRef.value?.reload()
  }
}

//Add to EmmyFolder
const selectEmmyFolderDialog = ref(false)

const manageOperationInEmmyFolderDialog = ref(false)

watch(selectEmmyFolderDialog, (v) => {
  if (!v) {
    handleAxiosPromise(operation, simulationApi.findById(props.id), {
      afterSuccess: () => {
        if (operation.value.value?.stepId === 80) {
          manageOperationInEmmyFolderDialog.value = true
        }
      },
      afterError: (err) => snackbarStore.setError(err ?? "Erreur lors de l'ajout de l'opération au dossier Emmy"),
    })
  }
})

watch(manageOperationInEmmyFolderDialog, (v) => {
  if (!v) {
    handleAxiosPromise(operation, simulationApi.findById(props.id), {
      afterError: (err) => snackbarStore.setError(err ?? "Erreur lors de la récupération de l'opération"),
    })
    operationEventHistoryRef.value?.reload()
  }
})

const removeOperationFromEmmyFolder = async () => {
  if (
    !(await dialogStore.addAlert({
      title: "Sortir l'opération du dossier EMMY",
      message: `Vous allez sortir l'opération "${operation.value.value?.operationName}" du dossier EMMY "${operation.value.value?.emmyFolder?.name}".\nVoulez-vous continuer ?`,
      maxWidth: '640px',
    }))
  ) {
    return
  }
  handleAxiosPromise(
    operation,
    emmyFolderApi.removeOperation(operation.value.value?.emmyFolder!.id ?? 0, operation.value.value?.id ?? 0),
    {
      afterSuccess: () => {
        operationEventHistoryRef.value?.reload()
        snackbarStore.setSuccess(`Opération ${operation.value.value?.operationName} retirée du dossier EMMY`)
      },
      afterError: () => {
        snackbarStore.setError(operation.value.error ?? "Echec de la sortie de l'opération du dossier emmy")
      },
    }
  )
}

const removeOperationFromControlOrderBatch = async () => {
  const controlOrderBatchId = operation.value.value?.controlOrderBatch?.id!
  if ((await operationApi.findAll({ controlOrderBatchId: controlOrderBatchId }, { size: 1 })).data.totalElements == 1) {
    if (!(await dialogStore.addAlert(deleteControlOrderBatchDialogRequest))) {
      return
    }
    await handleRemoveOperation()
    controlOrderBatchApi
      .delete(controlOrderBatchId)
      .then(() => {
        snackbarStore.setSuccess('Le lot de contrôle a bien été supprimé')
      })
      .catch(async (error) =>
        snackbarStore.setError(
          await handleAxiosException(error, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la suppression du lot de contrôle',
          })
        )
      )
    return
  }

  if (await dialogStore.addAlert(removeOperationFromControlOrderBatchRequest(operation.value.value!))) {
    handleRemoveOperation()
  }
}

const handleRemoveOperation = async () => {
  await handleAxiosPromise(
    operation,
    controlOrderBatchApi.removeOperation(
      operation.value.value?.controlOrderBatch!.id ?? 0,
      operation.value.value?.id ?? 0
    ),
    {
      afterSuccess: () => {
        operationEventHistoryRef.value?.reload()
        snackbarStore.setSuccess(`Opération ${operation.value.value?.operationName} retirée du lot de contrôle`)
      },
      afterError: () => {
        snackbarStore.setError(operation.value.error ?? "Echec de la sortie de l'opération du lot de contrôle")
      },
    }
  )
}

const operationEventHistoryRef = ref<typeof OperationEventHistoryView | null>(null)

const onSaveOperationDocument = (event: SendDocumentResult[]) => {
  if (event.some((it) => it.type === 'success')) {
    loadMissingDocuments()
    reload()
  }
}

const dialogStore = useDialogStore()

const check = (callback: () => void) => {
  if (cardRef.value) {
    cardRef.value?.check(callback)
  } else {
    callback()
  }
}

const cancelledOperation = () => {
  cancelDialog.value = false
  load()
}

const adminConfigurationStore = useAdminConfigurationStore()

const daysRemainingToValidateStep50 = computed((): number => {
  return differenceInDays(
    add(
      parseLocalDate(operation.value.value?.actualEndWorksDate ?? operation.value.value?.estimatedEndOperationDate!),
      {
        days: parseInt(adminConfigurationStore.numberOfDaysForValidateStep50?.data!),
      }
    ),
    new Date()
  )
})
const operationStepperRef = ref<typeof Stepper | null>(null)

const commentaireDialogRef = ref<typeof CommentaireDialog | undefined>()
const sendVF = () => {
  commentaireDialog.props.modelValue = true
  // Obligé de meetre dans nextTick car ici, sendVF est appelé trop tôt sinon, et certaines données sont incorrectement initialisé
  nextTick(() => {
    commentaireDialogRef.value!.sendVF()
  })
}

// Copied
const controlReportInfos = computed(() => {
  const date = operation.value.value?.controlReportIssueDate
  const limitDate = date ? addBusinessDay(date, CONTROL_REPORT_MAX_RETURN_DAYS) : undefined
  if (limitDate) {
    return {
      limitDate,
      differenceFromNowInDays: differenceInBusinessDays(limitDate, new Date()),
    }
  }
  return undefined
})
</script>

<style scoped>
.value__label {
  color: black;
  font-size: 1rem;
  margin-left: 1px; /* décalage horizontal */
  margin-top: 1px; /* décalage vertical */
}
</style>
