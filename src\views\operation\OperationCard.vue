<template>
  <VCard class="content-layout">
    <VCardTitle class="content-layout__header py-0 pe-0" style="min-height: 48px">
      <VRow align="center" class="h-100">
        <VCol> Détails <VProgressCircular v-if="loading" /> </VCol>
        <VCol class="d-flex flex-grow-0 gap-2">
          <div v-if="expandedDetail && localEdit" class="legend">
            <div class="legend">
              <div class="legend-item">
                <div class="legend-icon required"><VIcon icon="mdi-asterisk" size="x-small" /></div>
                <span>Requis</span>
              </div>
              <div class="legend-item">
                <div class="legend-icon recommended">✦</div>
                <span>Recommandé pour préremplir l'AH</span>
              </div>
            </div>
          </div>
          <template v-if="isOngoing">
            <NjIconBtn
              v-if="canEdit"
              icon="mdi-pencil"
              color="primary"
              rounded="0"
              :active="edit"
              @click="toggleEdit"
            />
            <NjIconBtn
              v-show="!userHasRole(userStore.currentUser, 'DAF', 'DAF_SIEGE')"
              :loading="generatePdfLoading"
              icon="mdi-file-pdf-box"
              color="primary"
              rounded="0"
              @click="generatePdf"
            />
            <NjIconBtn
              v-if="canDuplicateOperation"
              icon="mdi-file-multiple"
              color="primary"
              rounded="0"
              @click="
                operationFormRef ? operationFormRef.check(() => (duplicateDialog = true)) : (duplicateDialog = true)
              "
            />
            <NjIconBtn v-if="canDelete" icon="mdi-delete" color="error" rounded="0" @click="deleteSimulation" />
            <AlertDialog v-bind="deleteSimulationDialog.props" title="Supprimer la simulation" max-width="640px">
              Êtes-vous sûr de vouloir supprimer cette simulation?
            </AlertDialog>
          </template>
          <NjIconBtn
            v-if="operationView || simulationView"
            color="primary"
            :icon="expandedDetail ? 'mdi-arrow-collapse' : 'mdi-arrow-expand'"
            rounded="0"
            @click="expandedDetail = !expandedDetail"
          />

          <NjIconBtn
            v-if="!operationView && !simulationView"
            icon="mdi-window-close"
            variant="flat"
            color="primary"
            rounded="0"
            @click="emit('close')"
          />
        </VCol>
        <VCol v-if="!expandedDetail && localEdit" class="d-flex flex-grow-0 gap-2" style="margin-bottom: 16px">
          <div class="legend">
            <div class="legend">
              <div class="legend-item">
                <div class="legend-icon required"><VIcon icon="mdi-asterisk" size="x-small" /></div>
                <span>Requis</span>
              </div>
              <div class="legend-item">
                <div class="legend-icon recommended">✦</div>
                <span>Recommandé pour préremplir l'AH</span>
              </div>
            </div>
          </div>
        </VCol>
      </VRow>

      <CancelOperationDialog
        v-model="cancelDialog"
        :cancel-reason="cancelReason"
        :operation="operation"
        :cancelling-mail="sendCancellationMail"
        @cancelled="operationCancelled"
      />
      <DuplicateOperationDialog
        v-if="localOperation?.id"
        v-model="duplicateDialog"
        v-model:operation="localOperation"
        :disable-unsaved-data="operationFormRef?.disableUnsavedData"
        @update:operation="emit('update:operation', $event)"
      />
    </VCardTitle>
    <VDivider />
    <VCardText class="content-layout__main pa-0">
      <VAlert v-if="differentValuation" type="warning" variant="outlined" class="mb-2">
        Valorisation à modifier
      </VAlert>
      <VAlert v-if="disableValiderStep" type="info" variant="outlined" class="mb-2">
        L'opération doit être validé au niveau du
        <VLink
          :to="{
            name: 'OperationsGroupOneView',
            params: { id: localOperation?.operationsGroup!.id },
          }"
          >regroupement</VLink
        >
      </VAlert>
      <OperationFinalVersionToSend
        v-if="mode === 'drawer' && canSendFinalVersion(localOperation)"
        class="ma-4"
        :operation="localOperation"
        @sent="emit('request-reload')"
      />
      <VRow
        v-else-if="localOperation?.operationsGroup && !inOperationGroup"
        class="align-center"
        no-gutters
        style="font-size: 18px; font-weight: 700"
      >
        <VCol class="ml-2"> Regroupement </VCol>
        <VSpacer />
        <VCol class="flex-grow-0">
          <VLink
            :to="{
              name: 'OperationsGroupOneView',
              params: { id: localOperation?.operationsGroup!.id },
            }"
            icon="mdi-folder-outline"
            class="flex-grow-0"
            style="width: max-content"
          >
            {{ localOperation.operationsGroup.name }}
          </VLink>
        </VCol>
        <VDivider />
      </VRow>
      <OperationForm
        v-if="localEdit"
        ref="operationFormRef"
        v-model:operation="localOperation"
        edit
        :expanded-detail="expandedDetail"
        @update:operation="onUpdateOperation"
        @update:valuation="valuationChanged = true"
      />
      <OperationDetail
        v-else
        ref="operationDetailRef"
        v-model:operation="localOperation"
        v-model:expanded-detail="expandedDetail"
      />
      <CardDialog v-model="updatingValuation" title="Mettre à jour la valorisation" width="40%">
        Voulez-vous mettre à jour la valorisation avec les informations suivantes:
        <NjDisplayValue
          label="Valorisation classique"
          :value="`${localOperation?.classicValuationValue} →
              ${
                operationDetailRef?.valuations?.find((valuation: Valuation) => !valuation.precariousness)?.value ??
                operationFormRef?.valuations?.find((valuation: Valuation) => !valuation.precariousness)?.value
              }`"
        />
        <NjDisplayValue
          label="Valorisation précartité"
          :value="`${localOperation?.precariousnessValuationValue} →
              ${
                operationDetailRef?.valuations?.find((valuation: Valuation) => valuation.precariousness)?.value ??
                operationFormRef?.valuations?.find((valuation: Valuation) => valuation.precariousness)?.value
              }`"
        />
        <template #actions>
          <NjBtn variant="outlined" @click="updatingValuation = false">Non</NjBtn>
          <NjBtn @click="updateValuation"> Oui </NjBtn>
        </template>
      </CardDialog>
      <CommentaireDialog
        v-model="step60DafMailCommentaireDialog.props.modelValue"
        :operation="props.operation"
        :mandatory-message="dafMessage"
        no-link
        :advised-recipient="localOperation.entity.entityDetails.effectiveDafMail"
        @send="step60DafMailCommentaireDialog.props['onClick:positive']"
      />
    </VCardText>
    <VCardActions v-if="localEdit || differentValuation || !operationView" class="content-layout__footer pa-2" dense>
      <VSpacer />
      <NjBtn
        v-if="
          controlOrderBatchView &&
          operation.controlOrderBatch &&
          (!operation.controlOrderBatch.step || operation.controlOrderBatch.step === 'CONTROLLED_BY_CONTROL_OFFICE')
        "
        variant="outlined"
        @click="removeOperationFromControlOrderBatch"
      >
        Sortir du lot de contrôle
      </NjBtn>
      <NjBtn
        v-if="emmyFolderView && operation.stepId == 100"
        variant="outlined"
        color="error"
        @click="cancelOperationKoPncee"
      >
        Refusé par le PNCEE
      </NjBtn>
      <NjBtn
        v-if="emmyFolderView && operation.validateImportInEmmyDateTime && operation.stepId === 80"
        variant="outlined"
        color="error"
        @click="cancelEmmyValidate"
      >
        Annuler l'envoi EMMY
      </NjBtn>
      <NjBtn v-if="differentValuation && canEdit && !valuationChanged" @click="updatingValuation = true">
        Modifier valorisation
      </NjBtn>
      <template v-if="localEdit">
        <NjBtn variant="outlined" @click="localEdit = false"> Annuler </NjBtn>
        <NjBtn @click="saveOperation"> Enregistrer </NjBtn>
      </template>
      <template v-else-if="!operationView && !simulationView">
        <NjBtn
          v-if="
            operation.stepId == 70 &&
            !operation.controlOrderBatch &&
            userStore.isOperationManager &&
            displayControlOrderButtons
          "
          @click="controlOrderBatchDialog = true"
        >
          Ajouter à un lot de contrôle
          <ControlOrderBatchDialog
            v-model="controlOrderBatchDialog"
            :operation-ids="[operation.id]"
            :standardized-operation-sheet-id="operation.standardizedOperationSheet.id"
            :signed-date="operation.signedDate!"
            @add-in-control-order-batch="emit('saved')"
          />
        </NjBtn>
        <NjBtn
          v-if="operation.stepId > 0"
          :to="{
            name: 'OperationOneView',
            params: { id: operation.id },
          }"
        >
          Gérer l'opération
        </NjBtn>
        <NjBtn
          v-else
          :to="{
            name: 'SimulationOneView',
            params: { id: operation.id },
          }"
        >
          Gérer la simulation
        </NjBtn>
      </template>
    </VCardActions>
  </VCard>
</template>
<script lang="ts" setup>
import VLink from '@/components/VLink.vue'
import { useDialogStore } from '@/stores/dialog'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import { dafMail } from '@/types/message'
import type { Operation, OperationStatus } from '@/types/operation'
import { userHasRole } from '@/types/user'
import type { Valuation } from '@/types/valuation'
import { cloneDeep, isEqual } from 'lodash'
import type { PropType } from 'vue'
import {
  deleteControlOrderBatchDialogRequest,
  removeOperationFromControlOrderBatchRequest,
} from '../dialog/dialogRequest'
import DuplicateOperationDialog from './DuplicateOperationDialog.vue'
import OperationDetail from './OperationDetail.vue'
import OperationForm from './OperationForm.vue'
import CancelOperationDialog from './dialog/CancelOperationDialog.vue'
import CommentaireDialog from './dialog/CommentaireDialog.vue'
import ControlOrderBatchDialog from './dialog/ControlOrderBatchDialog.vue'
import OperationFinalVersionToSend from './OperationFinalVersionToSend.vue'
import { canSendFinalVersion } from '@/types/operation'

const props = defineProps({
  operation: {
    type: Object as PropType<Operation>,
    default: () => makeEmptyOperation(),
  },
  modelValue: Boolean,
  inOperationGroup: Boolean,
  loading: Boolean,
  edit: Boolean,
  mode: String as PropType<'detail' | 'drawer'>,
})

const emit = defineEmits<{
  'update:model-value': [value: boolean]
  'update:operation': [value: Operation]
  'update:edit': [boolean]
  close: [void]
  saved: [void]
  cancelOperationKOPNCEE: [void]
  'final-version-sent': []
  'request-reload': []
}>()

const snackbar = useSnackbarStore()
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

const localOperation = ref(cloneDeep(props.operation))
const displayControlOrderButtons = computed(() => import.meta.env.VITE_FEATURE_CONTROL_ORDER === 'true')

const expandedDetail = ref(false)
watch(
  () => props.modelValue,
  (v) => {
    if (v !== expandedDetail.value) {
      expandedDetail.value = v
    }
  },
  {
    immediate: true,
  }
)
const duplicateDialog = ref(false)
const deleteSimulationDialog = useConfirmAlertDialog()
const localEdit = ref(false)
const cancelDialog = ref(false)

const cancelReason = ref<OperationStatus | null>(null)

const cancelOperationKoPncee = () => {
  cancelReason.value = 'KO_PNCEE'
  cancelDialog.value = true
}

const operationCancelled = () => {
  emit('cancelOperationKOPNCEE')
  emit('close')
}

watch(
  () => props.edit,
  (v) => {
    if (v !== localEdit.value) {
      localEdit.value = v
    }
  }
)

const operationDetailRef = ref<typeof OperationDetail | null>(null)
const operationFormRef = ref<typeof OperationForm | null>(null)
const updatingValuation = ref(false)

const differentValuation = computed(
  () =>
    !(props.operation.atypicalClassicValuationValue || props.operation.atypicalPrecariousnessValuationValue) &&
    (operationDetailRef.value?.valuations?.length > 0 || operationFormRef.value?.valuations?.length > 0) &&
    ((localOperation.value.classicCumac &&
      localOperation.value.classicValuationValue !==
        (operationDetailRef.value?.valuations?.find((valuation: Valuation) => !valuation.precariousness)?.value ??
          operationFormRef.value?.valuations?.find((valuation: Valuation) => !valuation.precariousness)?.value)) ||
      (localOperation.value.precariousnessCumac &&
        localOperation.value.precariousnessValuationValue !==
          (operationDetailRef.value?.valuations?.find((valuation: Valuation) => valuation.precariousness)?.value ??
            operationFormRef.value?.valuations?.find((valuation: Valuation) => valuation.precariousness)?.value)))
)

const step60DafMailCommentaireDialog = useConfirmAlertDialog()
const dafMessage = ref('')

const valuationChanged = ref(false)
watch(localEdit, (v) => {
  if (!v) {
    valuationChanged.value = false
    if (localOperationBeforeUpdateValuation.value) {
      localOperation.value = cloneDeep(localOperationBeforeUpdateValuation.value)
    }
    localOperation.value = cloneDeep(props.operation)
  }
  emit('update:edit', v)
})

const localOperationBeforeUpdateValuation = ref<Operation | null>(null)
const updateValuation = async () => {
  localOperationBeforeUpdateValuation.value = cloneDeep(localOperation.value)
  const localOperationCopy = cloneDeep(localOperation.value)

  localOperationCopy.classicValuationValue =
    operationDetailRef.value?.valuations?.find((valuation: Valuation) => !valuation.precariousness)?.value ??
    operationFormRef.value?.valuations?.find((valuation: Valuation) => !valuation.precariousness)?.value

  localOperationCopy.precariousnessValuationValue =
    operationDetailRef.value?.valuations?.find((valuation: Valuation) => valuation.precariousness)?.value ??
    operationFormRef.value?.valuations?.find((valuation: Valuation) => valuation.precariousness)?.value

  if (!localEdit.value) {
    dafMessage.value = dafMail(localOperationCopy, false)
    if (localOperationCopy.stepId > 50 && !(await step60DafMailCommentaireDialog.confirm())) {
      updatingValuation.value = false
      return
    }

    localOperation.value = localOperationCopy

    operationApi
      .updateValuation(localOperation.value.id, {
        classicValuationValue: localOperationCopy.classicValuationValue,
        precariousnessValuationValue: localOperationCopy.precariousnessValuationValue,
      })
      .then((res) => {
        snackbar.setSuccess('La valorisation a été modifiée avec succès')
        emit('update:operation', res.data)
        emit('saved')
      })
      .catch(async (error) =>
        snackbar.setError(
          await handleAxiosException(error, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la modification de la valorisation',
          })
        )
      )
  } else {
    valuationChanged.value = true
  }
  updatingValuation.value = false
}

const sendCancellationMail = async () => {
  const oldClassicCumac = localOperation.value.classicCumac
  const oldPrecariousnessCumac = localOperation.value.precariousnessCumac

  localOperation.value.classicCumac = 0
  localOperation.value.precariousnessCumac = 0

  dafMessage.value = dafMail(localOperation.value, false)

  if (!(await step60DafMailCommentaireDialog.confirm())) {
    localOperation.value.classicCumac = oldClassicCumac
    localOperation.value.precariousnessCumac = oldPrecariousnessCumac
    cancelDialog.value = false
    return false
  }
  dafMessage.value = ''
  return true
}

const deleteSimulation = async () => {
  if (await deleteSimulationDialog.confirm())
    simulationApi
      .delete([localOperation.value!.id])
      .then(() => {
        snackbar.setSuccess('La simulation a été supprimée')
        router.push({ name: 'SimulationAllView' })
      })
      .catch(async (response) =>
        snackbar.setError(
          await handleAxiosException(response, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la suppression de la simulation',
          })
        )
      )
}

const saveOperation = async () => {
  await operationFormRef.value!.saveOperation()
}

const onUpdateOperation = (simulation: Operation) => {
  localEdit.value = false
  emit('update:operation', simulation)
  emit('saved')
}

const cancelEmmyValidate = () => {
  simulationApi
    .cancelEmmyImport(localOperation.value.id)
    .then((response) => {
      localOperation.value = response.data
      snackbar.setSuccess("L'import EMMY a été annulé avec succès")
      onUpdateOperation(localOperation.value)
    })
    .catch(async (error) =>
      snackbar.setError(
        await handleAxiosException(error, undefined, {
          defaultMessage: "Une erreur est survenue lors de l'annulation de l'import EMMY",
        })
      )
    )
}

const disableValiderStep = computed(
  () =>
    !!(
      localOperation.value &&
      localOperation.value.operationsGroup &&
      (localOperation.value.stepId == 30 || localOperation.value.stepId == 40)
    )
)

const isOngoing = computed(() => localOperation.value?.status === 'DOING')
const simulationView = computed(() => router.currentRoute.value.name === 'SimulationOneView')
const operationView = computed(() => router.currentRoute.value.name === 'OperationOneView')
const emmyFolderView = computed(() => router.currentRoute.value.name === 'EmmyFolderOneView')
const controlOrderBatchView = computed(() => router.currentRoute.value.name === 'ControlOrderBatchOneView')

const isSimulation = computed(() => (localOperation.value?.stepId ?? 0) === 0)

const currentUserHasAgencyRoleAndCreator = computed(
  () =>
    isSimulation.value && userStore.hasRole('AGENCE') && userStore.currentUser.gid === props.operation.creationUser.gid
)

const canEdit = computed(
  () =>
    (isSimulation.value && currentUserHasAgencyRoleAndCreator.value) ||
    (userStore.isOperationManager && (localOperation.value?.stepId ?? 0) < 60) ||
    (userStore.isSiege &&
      (localOperation.value?.stepId ?? 0) <= 80 &&
      !localOperation.value.validateImportInEmmyDateTime) ||
    userStore.isAdminPlus
)

const toggleEdit = () => {
  if (localEdit.value) {
    !operationFormRef.value!.check(() => {
      localEdit.value = !localEdit.value
    })
  } else {
    localEdit.value = !localEdit.value
  }
}

const canDelete = computed(
  () =>
    isSimulation.value &&
    localOperation.value?.id &&
    (currentUserHasAgencyRoleAndCreator.value ||
      userStore.hasRole('AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'SIEGE', 'INSTRUCTEUR', 'ADMIN', 'ADMIN_PLUS'))
)

watch(
  () => props.operation,
  (v) => {
    if (v && !isEqual(v, localOperation.value)) {
      localOperation.value = cloneDeep(v)
    }
  },
  {
    deep: true,
  }
)

watch(route, (v) => {
  if (v.query?.duplicate === 'true') {
    expandedDetail.value = true
    localEdit.value = true
  }
})

watch(expandedDetail, (v) => emit('update:model-value', v))

const generatePdfLoading = ref(false)
const generatePdf = () => {
  generatePdfLoading.value = true
  operationApi
    .exportOneToPdf(props.operation.id)
    .then((r) => {
      downloadFile('export opération.pdf', r.data)
    })
    .catch(async (err) => {
      snackbar.setError(
        await handleAxiosException(err, undefined, {
          defaultMessage: "Echec lors de l'export de l'opération",
        })
      )
    })
    .finally(() => (generatePdfLoading.value = false))
}

onMounted(() => {
  if (route.query?.duplicate === 'true') {
    expandedDetail.value = true
    localEdit.value = true
  }
})

const controlOrderBatchDialog = ref(false)

const dialogStore = useDialogStore()
const removeOperationFromControlOrderBatch = async () => {
  const controlOrderBatchId = props.operation?.controlOrderBatch?.id!
  if ((await operationApi.findAll({ controlOrderBatchId: controlOrderBatchId }, { size: 1 })).data.totalElements == 1) {
    if (!(await dialogStore.addAlert(deleteControlOrderBatchDialogRequest))) {
      return
    }
    await handleRemoveOperationFromControlOrderBatch()
    controlOrderBatchApi
      .delete(controlOrderBatchId)
      .then(() => {
        snackbar.setSuccess('Le lot de contrôle a bien été supprimé')
      })
      .catch(async (error) =>
        snackbar.setError(
          await handleAxiosException(error, undefined, {
            defaultMessage: 'Une erreur est survenue lors de la suppression du lot de contrôle',
          })
        )
      )
    return
  }

  if (!(await dialogStore.addAlert(removeOperationFromControlOrderBatchRequest(props.operation!)))) {
    return
  }
  handleRemoveOperationFromControlOrderBatch()
}

const handleRemoveOperationFromControlOrderBatch = async () => {
  await controlOrderBatchApi
    .removeOperation(props.operation.controlOrderBatch!.id ?? 0, props.operation.id ?? 0)
    .then(() => {
      snackbar.setSuccess(`Opération ${props.operation.operationName} retirée du lot de contrôle`)
      emit('update:operation', { ...props.operation, controlOrderBatch: null })
      emit('saved')
      emit('close')
    })
    .catch(async (err) => {
      snackbar.setError((await handleAxiosException(err)) ?? "Echec de la sortie de l'opération du lot de contrôle")
    })
}

const canDuplicateOperation = computed(
  () =>
    localOperation.value?.id &&
    (localOperation.value?.stepId ?? 0) < 60 &&
    ((localOperation.value?.stepId > 0 &&
      userStore.hasRole('AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')) ||
      (localOperation.value?.stepId == 0 &&
        userStore.hasRole('AGENCE', 'SUPPORT_AGENCE_PLUS', 'AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')))
)

watch(
  () => localOperation.value.id,
  (v, oldV) => {
    if (v !== oldV) {
      expandedDetail.value = false
    }
  }
)

defineExpose({
  differentValuation: differentValuation,
  updatingValuation: updatingValuation,
  localEdit: localEdit,
  operationFormRef: operationFormRef,
  check: (callback: () => void) => {
    if (operationFormRef.value) {
      operationFormRef.value?.check(callback)
    } else {
      callback()
    }
  },
})
</script>

<style scoped lang="scss">
$required-bg-color: #e7eefc;
$required-text-color: #007acd;
$recommended-bg-color: #f2ecf7;
$recommended-text-color: #744299;
$icon-size: 24px;
$icon-padding: 4px;
$font-size: 0.875rem;
$gap-between-items: 8px;
$gap-between-legend: 16px;

.legend {
  display: flex;
  gap: $gap-between-legend;
  align-items: center;

  .legend-item {
    display: flex;
    align-items: center;
    gap: $gap-between-items;
    font-size: $font-size;

    .legend-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      padding: $icon-padding;
      border-radius: 4px;
      width: $icon-size;
      height: $icon-size;

      &.required {
        background-color: $required-bg-color;
        color: $required-text-color;
      }

      &.recommended {
        background-color: $recommended-bg-color;
        color: $recommended-text-color;
      }
    }

    span {
      color: inherit;
    }
  }
}
</style>
